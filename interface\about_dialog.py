from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QPushButton, 
                             QTabWidget, QWidget, QTextBrowser)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap, QFont

class AboutDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """Initialize the about dialog UI."""
        self.setWindowTitle('About Blighter Viewshed Analysis Tool')
        self.setModal(True)
        self.resize(600, 400)

        layout = QVBoxLayout(self)
        
        # Create tab widget
        tab_widget = QTabWidget()
        
        # Add tabs
        tab_widget.addTab(self.create_about_tab(), "About")
        tab_widget.addTab(self.create_license_tab(), "License")
        tab_widget.addTab(self.create_credits_tab(), "Credits")
        tab_widget.addTab(self.create_changelog_tab(), "Changelog")
        
        layout.addWidget(tab_widget)
        
        # Add close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def create_about_tab(self):
        """Create the about tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Add application name
        app_name = QLabel("Blighter Viewshed Analysis Tool")
        app_name.setFont(QFont('Arial', 14, QFont.Weight.Bold))
        app_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(app_name)

        # Add version
        version = QLabel("Version 1.0.0")
        version.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(version)

        # Add description
        description = QTextBrowser()
        description.setOpenExternalLinks(True)
        description.setHtml("""
        <p>The Viewshed Analysis Tool is a professional-grade application for terrain visibility analysis 
        and RF propagation modeling. It combines advanced terrain data processing with an intuitive user 
        interface to provide accurate visibility assessments for various applications.</p>
        
        <p><b>Key Features:</b></p>
        <ul>
            <li>Advanced terrain visibility analysis using SRTM data</li>
            <li>Multiple elevation data sources (SRTM, Google Elevation API)</li>
            <li>Customizable visualization options with multiple color schemes</li>
            <li>Export capabilities (KML, CSV, GeoJSON)</li>
            <li>Integration with AI/ML models for enhanced analysis</li>
            <li>Comprehensive range rings and coverage visualization</li>
            <li>Real-time terrain data processing</li>
            <li>Support for various map providers</li>
        </ul>
        
        <p><b>System Requirements:</b></p>
        <ul>
            <li>Operating System: Windows 10/11, macOS 10.15+, Linux</li>
            <li>Python 3.8 or higher</li>
            <li>Minimum 8GB RAM recommended</li>
            <li>Internet connection for SRTM data download</li>
        </ul>
        
        <p><b>Support:</b></p>
        <p>For support, please visit our website or contact technical support:</p>
        <p>Website: <a href="https://www.blighter.com">www.blighter.com</a></p>
        <p>Email: <EMAIL></p>
        <p>Documentation: <a href="https://docs.blighter.com/viewshed">docs.blighter.com/viewshed</a></p>
        """)
        layout.addWidget(description)

        return tab

    def create_license_tab(self):
        """Create the license tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        license_text = QTextBrowser()
        license_text.setHtml("""
        <h3>Software License Agreement</h3>
        
        <p>© 2024 Blighter Surveillance Systems Ltd. All rights reserved.</p>
        
        <p>This software is proprietary and confidential. Unauthorized copying, transfer, or use 
        of this software, its documentation, or any related materials is strictly prohibited.</p>
        
        <p>The software includes components from the following open-source projects:</p>
        <ul>
            <li>PyQt6 - GNU GPL v3</li>
            <li>Folium - MIT License</li>
            <li>NumPy - BSD License</li>
            <li>GDAL - MIT License</li>
            <li>Elevation - MIT License</li>
            <li>Requests - Apache License 2.0</li>
            <li>SimpleKML - LGPL License</li>
        </ul>

        <p>Third-party services used by this software may be subject to additional terms and conditions:</p>
        <ul>
            <li>SRTM Data - NASA/USGS terms of use</li>
            <li>OpenStreetMap - OpenStreetMap License</li>
            <li>Google Elevation API - Google Maps Platform Terms of Service</li>
        </ul>

        <p>For full license terms and conditions, please refer to the documentation.</p>
        """)
        layout.addWidget(license_text)

        return tab

    def create_credits_tab(self):
        """Create the credits tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        credits_text = QTextBrowser()
        credits_text.setHtml("""
        <h3>Credits and Acknowledgments</h3>

        <p><b>Development Team:</b></p>
        <ul>
            <li>Lead Developer: Aleksander Celewicz</li>
            <li>UI/UX Design: Aleksander Celewicz</li>
            <li>Algorithm Development: Aleksander Celewicz</li>
            <li>Testing: Quality Assurance Team(Aleksander Celewicz)</li>
        </ul>

        <p><b>Special Thanks:</b></p>
        <ul>
            <li>NASA/USGS for SRTM data access</li>
            <li>OpenStreetMap contributors</li>
            <li>PyQt development team</li>
            <li>Folium project contributors</li>
            <li>Beta testing team members</li>
        </ul>

        <p><b>Data Sources:</b></p>
        <ul>
            <li>SRTM (Shuttle Radar Topography Mission) - NASA/USGS</li>
            <li>OpenStreetMap © OpenStreetMap contributors</li>
            <li>Satellite imagery providers</li>
            <li>Local terrain data contributors</li>
        </ul>

        <p><b>Tools and Libraries:</b></p>
        <ul>
            <li>PyQt6 - UI Framework</li>
            <li>Folium - Map Visualization</li>
            <li>GDAL - Geospatial Data Processing</li>
            <li>NumPy - Numerical Computations</li>
            <li>Elevation - Terrain Data Processing</li>
        </ul>
        """)
        layout.addWidget(credits_text)

        return tab

    def create_changelog_tab(self):
        """Create the changelog tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        changelog_text = QTextBrowser()
        changelog_text.setHtml("""
        <h3>Changelog</h3>

        <p><b>Version 1.0.0 (2024-12-14)</b></p>
        <ul>
            <li>Initial release</li>
            <li>Basic viewshed analysis functionality</li>
            <li>SRTM data integration</li>
            <li>Multiple map provider support</li>
            <li>KML export capability</li>
            <li>Range rings visualization</li>
        </ul>

        <p><b>Version 1.0.1 (Upcoming)</b></p>
        <ul>
            <li>Enhanced performance for large areas</li>
            <li>Additional color schemes</li>
            <li>Improved error handling</li>
            <li>Bug fixes and stability improvements</li>
        </ul>

        <p><b>Known Issues:</b></p>
        <ul>
            <li>Large area calculations may require significant memory</li>
            <li>Some map providers may have loading delays</li>
            <li>SRTM data download might be slow in certain regions</li>
        </ul>

        <p><b>Planned Features:</b></p>
        <ul>
            <li>Batch processing capabilities</li>
            <li>Advanced RF propagation modeling</li>
            <li>Custom color palette editor</li>
            <li>Integration with more elevation data sources</li>
            <li>Enhanced reporting features</li>
        </ul>
        """)
        layout.addWidget(changelog_text)

        return tab