#!/usr/bin/env python3
"""
Test script to verify that the 'multiple values for keyword argument max_range_km' error is fixed.
This test focuses only on the parameter conflict resolution.
"""

import os
import sys
import numpy as np

# Add the parent directory to the path to import the main module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from visibility_2d import Visibility2DAnalyzer
from utils.multipath_analyzer import MultipathAnalyzer

def test_parameter_conflict_resolution():
    """Test that the parameter conflict error is resolved."""
    print("Testing Parameter Conflict Resolution")
    print("=" * 50)
    
    # Test parameters
    test_lat = 50.0
    test_lon = -4.0
    test_hgt_file = "srtm_data/N50W004.hgt"
    
    # Create analyzer and multipath analyzer
    analyzer = Visibility2DAnalyzer(test_hgt_file, test_lat, test_lon)
    multipath_analyzer = MultipathAnalyzer(frequency=16e9)
    
    # Set up multipath analyzer
    multipath_analyzer.set_geometry(50.0, 2.0, 30.0)
    multipath_analyzer.set_analysis_range(5.0)
    multipath_analyzer.calculate_multipath_gain()
    
    # Create test visibility data
    size = 50  # Smaller for faster testing
    visibility = np.zeros((size, size), dtype=bool)
    center = size // 2
    radius = size // 4
    y, x = np.ogrid[:size, :size]
    mask = (x - center)**2 + (y - center)**2 <= radius**2
    visibility[mask] = True
    
    # Define bounds
    bounds = {
        'north': test_lat + 0.01,
        'south': test_lat - 0.01,
        'east': test_lon + 0.01,
        'west': test_lon - 0.01
    }
    
    # Create multipath parameters that include max_range_km (this was causing the conflict)
    multipath_params = {
        'max_range_km': 5.0,  # This parameter was conflicting
        'effect_threshold_db': -30,
        'color_scheme': 'Rainbow (dB-based)',
        'opacity': 0.6,
        'show_nulls_only': False,
        'show_peaks_only': False,
        'smooth_transitions': True
    }
    
    print(f"Testing scenario that previously caused error:")
    print(f"  multipath_params contains 'max_range_km': {multipath_params['max_range_km']}")
    print(f"  export_to_kml also receives detection_distance parameter")
    print(f"  This should NOT cause 'multiple values for keyword argument' error")
    
    # Test output file
    output_file = "Test Files/test_parameter_conflict.kml"
    
    try:
        # This call previously failed with:
        # "utils.multipath_analyzer.MultipathAnalyzer.calculate_multipath_polygons() 
        #  got multiple values for keyword argument 'max_range_km'"
        analyzer.export_to_kml(
            visibility=visibility,
            bounds=bounds,
            output_file=output_file,
            enable_rings=True,
            ring_interval=1,
            detection_distance=5,  # This was conflicting with max_range_km in multipath_params
            opacity=0.8,
            color_pattern='Red-Blue Gradient',
            enable_multipath_rings=True,
            multipath_analyzer=multipath_analyzer,
            multipath_params=multipath_params
        )
        
        # If we get here, the parameter conflict is resolved
        print(f"✓ SUCCESS: No parameter conflict error!")
        print(f"✓ KML export completed without 'multiple values for keyword argument' error")
        
        # Check if files were created
        kml_exists = os.path.exists(output_file)
        overlay_exists = os.path.exists(output_file.replace('.kml', '_overlay.png'))
        
        if kml_exists and overlay_exists:
            print(f"✓ Both KML and PNG files created successfully")
            return True
        else:
            print(f"⚠ Files creation issue, but parameter conflict is resolved")
            return True  # Parameter conflict is still resolved
            
    except Exception as e:
        error_msg = str(e)
        if "multiple values for keyword argument 'max_range_km'" in error_msg:
            print(f"✗ FAILED: Parameter conflict still exists!")
            print(f"  Error: {error_msg}")
            return False
        elif "got multiple values for keyword argument" in error_msg:
            print(f"✗ FAILED: Similar parameter conflict detected!")
            print(f"  Error: {error_msg}")
            return False
        else:
            print(f"✓ Parameter conflict resolved, but other error occurred:")
            print(f"  Error: {error_msg}")
            return True  # Parameter conflict is resolved, other errors are separate issues

if __name__ == "__main__":
    print("Testing Parameter Conflict Resolution")
    print("=" * 60)
    
    success = test_parameter_conflict_resolution()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Parameter conflict resolution test PASSED!")
        print("✓ 'multiple values for keyword argument max_range_km' error is FIXED")
        print("✓ KML export with multipath parameters works without conflict")
        sys.exit(0)
    else:
        print("❌ Parameter conflict resolution test FAILED!")
        print("✗ 'multiple values for keyword argument max_range_km' error still exists")
        sys.exit(1) 