# SRTM File Operations Handler
# 
# Responsibilities:
# - Load and parse SRTM .hgt files
# - Handle SRTM data caching
# - Provide elevation data for specific coordinates
# - Convert SRTM data to usable formats
# - Validate SRTM file integrity
#
# Key Functions:
# - load_srtm_file(file_path) -> elevation_data
# - get_elevation(lat, lon) -> float
# - validate_srtm_file(file_path) -> bool
# - cache_srtm_data(data, cache_path)

import numpy as np
import os
from .srtm_downloader import download_srtm_tile

def get_coords_from_filename(filepath):
    """Extract coordinates from SRTM filename (e.g., 'N50W004.hgt')."""
    filename = os.path.basename(filepath)
    lat = int(filename[1:3])
    lon = int(filename[4:7])
    if filename[0] == 'S':
        lat = -lat
    if filename[3] == 'W':
        lon = -lon
    return lat, lon

def load_srtm_file(file_path, auto_download=True, progress_callback=None):
    """
    Loads SRTM .hgt file and returns the elevation data as a numpy array.
    Automatically detects SRTM1 (3601x3601) or SRTM3 (1201x1201) format.
    If file doesn't exist and auto_download is True, attempts to download it.

    Args:
        file_path: Path to the SRTM file
        auto_download: Whether to automatically download missing files
        progress_callback: Optional callback for download progress

    Returns:
        tuple: (elevation_data, format_info) where format_info contains 'size' and 'pixels_per_degree'
               Returns (None, None) if loading failed
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path) and auto_download:
            # Extract coordinates from filename for download
            try:
                lat, lon = get_coords_from_filename(file_path)
                print(f"SRTM file not found, attempting download for {lat}, {lon}")

                # Download the file
                downloaded_path = download_srtm_tile(lat, lon, progress_callback=progress_callback)
                if downloaded_path and os.path.exists(downloaded_path):
                    file_path = downloaded_path
                    print(f"Successfully downloaded SRTM file: {downloaded_path}")
                else:
                    print(f"Failed to download SRTM file for {lat}, {lon}")
                    return None, None
            except Exception as e:
                print(f"Error during SRTM download: {e}")
                return None, None

        # Determine file format by size
        file_size = os.path.getsize(file_path)

        # SRTM1: 3601x3601 pixels = 25,927,202 bytes
        # SRTM3: 1201x1201 pixels = 2,884,802 bytes
        if abs(file_size - 25927202) < 10000:  # Allow some tolerance
            # SRTM1 format
            size = 3601
            pixels_per_degree = 3600
            print(f"Detected SRTM1 format (3601x3601)")
        elif abs(file_size - 2884802) < 1000:  # Allow some tolerance
            # SRTM3 format
            size = 1201
            pixels_per_degree = 1200
            print(f"Detected SRTM3 format (1201x1201)")
        else:
            print(f"Unknown SRTM format. File size: {file_size} bytes")
            return None, None

        # Load the file
        with open(file_path, 'rb') as f:
            data = np.fromfile(f, dtype='>i2', count=size*size)
            elevation_data = data.reshape((size, size))

        format_info = {
            'size': size,
            'pixels_per_degree': pixels_per_degree,
            'format': 'SRTM1' if size == 3601 else 'SRTM3'
        }

        return elevation_data, format_info

    except Exception as e:
        print(f"Error loading SRTM file: {e}")
        return None, None

def get_elevation(lat, lon, elevation_data, format_info=None, hgt_filepath=None):
    """
    Returns the elevation for the given coordinates by extracting from SRTM data.

    Args:
        lat (float): Latitude in decimal degrees
        lon (float): Longitude in decimal degrees
        elevation_data (np.ndarray): SRTM elevation data array
        format_info (dict, optional): Format information from load_srtm_file
        hgt_filepath (str, optional): Path to the HGT file to get tile coordinates

    Returns:
        float: Elevation in meters, or None if coordinates are out of bounds
    """
    if elevation_data is None:
        return None

    # Determine pixels per degree from format info or data shape
    if format_info and 'pixels_per_degree' in format_info:
        pixels_per_degree = format_info['pixels_per_degree']
    else:
        # Auto-detect from array shape
        if elevation_data.shape[0] == 3601:
            pixels_per_degree = 3600  # SRTM1
        elif elevation_data.shape[0] == 1201:
            pixels_per_degree = 1200  # SRTM3
        else:
            print(f"Unknown SRTM format with shape {elevation_data.shape}")
            return None

    # Get the southwest corner coordinates from the filename
    if hgt_filepath:
        try:
            sw_lat, sw_lon = get_coords_from_filename(hgt_filepath)
        except:
            # Fallback: assume the data covers the coordinate range
            sw_lat = int(lat)
            sw_lon = int(lon)
    else:
        # Fallback: assume the data covers the coordinate range
        sw_lat = int(lat)
        sw_lon = int(lon)

    # Calculate pixel coordinates within the tile
    # SRTM data is arranged with north at top (row 0), so we need to flip latitude
    lat_pixel = int(round((sw_lat + 1 - lat) * pixels_per_degree))
    lon_pixel = int(round((lon - sw_lon) * pixels_per_degree))

    # Check bounds
    if 0 <= lat_pixel < elevation_data.shape[0] and 0 <= lon_pixel < elevation_data.shape[1]:
        elevation = elevation_data[lat_pixel, lon_pixel]
        # Handle SRTM no-data values (-32768)
        if elevation == -32768:
            return None
        return float(elevation)
    else:
        return None
