import os
import numpy as np
import matplotlib.pyplot as plt
from simplekml import Kml, Color
import math
from plotting_utils import plot_2d_visibility_with_rings
from scipy.ndimage import gaussian_filter, binary_fill_holes
from skimage import measure
from elevation.water_mask_handler import WaterMaskHandler
from utils.multipath_analyzer import MultipathAnalyzer
from utils.propagation_models import PropagationModels
from utils.color_utils import ColorUtils
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing
import geojson
from shapely.geometry import Polygon, MultiPolygon

# Previously tested utility functions
def get_coords_from_filename(filepath):
    """
    Extract coordinates from SRTM filename (e.g., 'N50W004.hgt').
    Returns the SW corner coordinates in decimal degrees.
    """
    filename = os.path.basename(filepath)
    lat = int(filename[1:3])
    if filename[0] == 'S':
        lat = -lat
    lon = int(filename[4:7])
    if filename[3] == 'W':
        lon = -lon
    return lat, lon

class Visibility2DAnalyzer:
    def __init__(self, hgt_file, lat, lon):
        self.hgt_file = hgt_file
        self.observer_lat = lat
        self.observer_lon = lon
        self.data = None
        self.pixel_size = 30  # Approximate SRTM1 resolution in meters
        # Effective Earth radius (4/3 of actual radius) to account for atmospheric refraction
        self.EFFECTIVE_EARTH_RADIUS = 8500000  # meters

    def analyze_multipath_effects(self, radar_height, target_height, max_range_km,
                                  frequency, land_reflectivity, water_reflectivity,
                                  multipath_params):
        """
        Performs a comprehensive multipath analysis, generating data for both the
        main visibility overlay and the decorative multipath rings.
        Returns:
            ring_polygons: List of multipath ring polygons for map rendering
        """
        analyzer = MultipathAnalyzer(frequency=frequency * 1e9)
        analyzer.set_geometry(radar_height, target_height, land_reflectivity * 100) # Use land as default
        analyzer.set_analysis_range(max_range_km)
        ring_polygons = analyzer.calculate_multipath_polygons(
            center_lat=self.observer_lat,
            center_lon=self.observer_lon,
            max_range_km=max_range_km,
            **multipath_params
        )
        return ring_polygons

    def read_hgt(self):
        """Read and parse HGT file (3601x3601 samples)."""
        try:
            with open(self.hgt_file, 'rb') as f:
                data = np.fromfile(f, np.dtype('>i2'), 3601*3601)
                self.data = data.reshape((3601, 3601))
                print(f"Successfully read HGT file. Shape: {self.data.shape}")
        except Exception as e:
            print(f"Error reading HGT file: {str(e)}")
            raise

    def calculate_great_circle_distance(self, lat1, lon1, lat2, lon2):
        """Calculate great circle distance between two points in meters."""
        R = 6371000  # Earth's radius in meters
        
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))
        
        return R * c

    def _get_multipath_gain(self, distance, radar_height, target_height, frequency, reflectivity):
        """
        Get multipath gain (dB) at a given distance using the MultipathAnalyzer.
        This is a wrapper around the authoritative MultipathAnalyzer implementation.

        Args:
            distance (float): Distance from the radar (meters).
            radar_height (float): Height of the radar (meters).
            target_height (float): Height of the target (meters).
            frequency (float): Frequency of the radar signal (GHz).
            reflectivity (float): Reflectivity of the terrain (0.0 to 1.0).

        Returns:
            float: Multipath gain in dB.
        """
        # Create a temporary MultipathAnalyzer instance for this calculation
        analyzer = MultipathAnalyzer(frequency=frequency * 1e9)  # Convert GHz to Hz
        analyzer.set_geometry(radar_height, target_height, reflectivity * 100)  # Convert to percentage
        
        # Calculate gain for this specific distance
        distances = np.array([distance])
        _, gains = analyzer.calculate_multipath_gain(distances=distances)
        
        return gains[0] if len(gains) > 0 else 0.0

    def _calculate_visibility_row(self, i, section_size, valid_points, distances, elevation_data, observer_pos, observer_elev, v_beam_min, v_beam_max, lat_grid, lon_grid, water_handler, sw_lat, sw_lon, water_mask, observer_height, target_height, land_reflectivity, water_reflectivity, propagation_models, temperature_c, pressure_hpa, humidity_percent, rain_rate_mm_per_hour, polarization, foliage_depth_m, foliage_type):
        row_visibility = np.zeros(section_size[1], dtype=bool)
        for j in range(section_size[1]):
            if valid_points[i, j]:
                distance_to_point = distances[i, j]
                if self.has_line_of_sight(observer_pos, (i, j), elevation_data,
                                        observer_elev, elevation_data[i, j], v_beam_min, v_beam_max):
                    target_lat, target_lon = lat_grid[i, j], lon_grid[i, j]
                    refl_lat, refl_lon = water_handler.get_reflection_point(
                        self.observer_lat, self.observer_lon,
                        target_lat, target_lon,
                        observer_height, target_height
                    )
                    is_water = water_handler.is_water(refl_lat, refl_lon, sw_lat, sw_lon, water_mask)
                    current_reflectivity = water_reflectivity if is_water else land_reflectivity
                    multipath_gain = self._get_multipath_gain(
                        distance_to_point,
                        observer_height,
                        elevation_data[i,j] + target_height,
                        self.frequency,
                        current_reflectivity
                    )
                    if propagation_models:
                        obstacle_height = self._find_max_obstacle_height(
                            observer_pos, (i, j), elevation_data, observer_elev
                        )
                        propagation_losses = propagation_models.calculate_total_propagation_loss(
                            frequency_hz=self.frequency * 1e9,
                            distance_m=distance_to_point,
                            radar_height_m=observer_height,
                            target_height_m=elevation_data[i,j] + target_height,
                            obstacle_height_m=obstacle_height,
                            temperature_c=temperature_c,
                            pressure_hpa=pressure_hpa,
                            humidity_percent=humidity_percent,
                            rain_rate_mm_per_hour=rain_rate_mm_per_hour,
                            polarization=polarization,
                            foliage_depth_m=foliage_depth_m,
                            foliage_type=foliage_type
                        )
                        total_propagation_loss = propagation_losses['total'] - propagation_losses['free_space']
                        adjusted_multipath_gain = multipath_gain - total_propagation_loss
                    else:
                        adjusted_multipath_gain = multipath_gain
                    if adjusted_multipath_gain >= -10:
                        row_visibility[j] = True
        return row_visibility

    def calculate_visibility(self, observer_height, radius_km=10, v_beam_min=0, v_beam_max=0,
                             start_angle=0, end_angle=360, target_height=0, frequency=10, 
                             land_reflectivity=0.3, water_reflectivity=0.9, 
                             progress_callback=None, enable_propagation_models=False,
                             temperature_c=15, pressure_hpa=1013.25, humidity_percent=60,
                             rain_rate_mm_per_hour=0, polarization='horizontal',
                             foliage_depth_m=0, foliage_type='dense', use_parallel_processing=True):
        """Calculate visibility using radar scanning approach with land/water differentiation and advanced propagation models."""
        if self.data is None:
            self.read_hgt()

        # Initialize water mask handler and propagation models
        water_handler = WaterMaskHandler()
        propagation_models = PropagationModels() if enable_propagation_models else None
        
        hgt_filename = os.path.basename(self.hgt_file)
        water_mask = water_handler.get_water_mask_for_tile(hgt_filename)
        
        sw_lat, sw_lon = get_coords_from_filename(self.hgt_file)
        radius_meters = radius_km * 1000
        
        # Calculate analysis bounds to cover full circle
        # We'll use a square that encompasses our circle
        meters_per_degree_lat = 111320  # Approximate meters per degree at equator
        
        # Calculate degrees needed for radius (use larger margin to ensure full coverage)
        margin_factor = np.sqrt(2) * 1.5  # Account for diagonal distance plus safety margin
        deg_lat = radius_meters / meters_per_degree_lat * margin_factor
        deg_lon = deg_lat / np.cos(np.radians(self.observer_lat))  # Adjust for latitude
        
        # Calculate pixel bounds
        pixels_per_degree = 3600  # SRTM1 resolution
        observer_row = int(round((sw_lat + 1 - self.observer_lat) * pixels_per_degree))
        observer_col = int(round((self.observer_lon - sw_lon) * pixels_per_degree))
        
        # Calculate section bounds with increased margin
        radius_pixels = int(round(radius_meters / self.pixel_size * margin_factor))
        
        start_row = max(0, observer_row - radius_pixels)
        end_row = min(self.data.shape[0], observer_row + radius_pixels + 1)
        start_col = max(0, observer_col - radius_pixels)
        end_col = min(self.data.shape[1], observer_col + radius_pixels + 1)
        
        # Extract elevation data for analysis section
        elevation_data = self.data[start_row:end_row, start_col:end_col]
        section_size = elevation_data.shape
        visibility = np.zeros(section_size, dtype=bool)
        
        # Calculate local observer position
        local_observer_row = observer_row - start_row
        local_observer_col = observer_col - start_col
        observer_pos = (local_observer_row, local_observer_col)
        observer_elev = self.data[observer_row, observer_col] + observer_height

        self.target_height = target_height
        self.frequency = frequency
        
        # Calculate lat/lon for each point in our section
        lats = np.linspace(
            sw_lat + 1 - start_row/pixels_per_degree,
            sw_lat + 1 - end_row/pixels_per_degree,
            section_size[0]
        )
        lons = np.linspace(
            sw_lon + start_col/pixels_per_degree,
            sw_lon + end_col/pixels_per_degree,
            section_size[1]
        )
        
        # Create meshgrid for vectorized calculations
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        # Calculate distances to all points at once
        distances = self.calculate_great_circle_distance(
            self.observer_lat, self.observer_lon,
            lat_grid, lon_grid
        )
        
        # Create distance mask
        distance_mask = distances <= radius_meters
        
        # Calculate angles for horizontal FOV check
        y_grid = lat_grid - self.observer_lat
        x_grid = (lon_grid - self.observer_lon) * np.cos(np.radians(self.observer_lat))
        angles = np.degrees(np.arctan2(y_grid, x_grid)) % 360
        
        # Create FOV mask
        if start_angle <= end_angle:
            fov_mask = (angles >= start_angle) & (angles <= end_angle)
        else:  # Handling wrap around 360°
            fov_mask = (angles >= start_angle) | (angles <= end_angle)
        
        # Combine masks
        valid_points = distance_mask & fov_mask
        
        # Calculate visibility only for valid points (with optional parallel processing)
        if use_parallel_processing and section_size[0] > 10:  # Only use parallel for larger grids
            # Determine optimal number of workers (use CPU count but cap at grid size)
            max_workers = min(multiprocessing.cpu_count(), section_size[0])
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all row calculations
                future_to_row = {
                    executor.submit(
                        self._calculate_visibility_row,
                        i, section_size, valid_points, distances, elevation_data, 
                        observer_pos, observer_elev, v_beam_min, v_beam_max, 
                        lat_grid, lon_grid, water_handler, sw_lat, sw_lon, water_mask,
                        observer_height, target_height, land_reflectivity, water_reflectivity,
                        propagation_models, temperature_c, pressure_hpa, humidity_percent,
                        rain_rate_mm_per_hour, polarization, foliage_depth_m, foliage_type
                    ): i for i in range(section_size[0])
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_row):
                    row_idx = future_to_row[future]
                    try:
                        row_visibility = future.result()
                        visibility[row_idx, :] = row_visibility
                        
                        # Update progress if callback provided
                        if progress_callback:
                            progress_callback((row_idx + 1) / section_size[0] * 100)
                            
                    except Exception as e:
                        print(f"Error processing row {row_idx}: {e}")
                        # Fall back to sequential processing for this row
                        visibility[row_idx, :] = self._calculate_visibility_row(
                            row_idx, section_size, valid_points, distances, elevation_data,
                            observer_pos, observer_elev, v_beam_min, v_beam_max,
                            lat_grid, lon_grid, water_handler, sw_lat, sw_lon, water_mask,
                            observer_height, target_height, land_reflectivity, water_reflectivity,
                            propagation_models, temperature_c, pressure_hpa, humidity_percent,
                            rain_rate_mm_per_hour, polarization, foliage_depth_m, foliage_type
                        )
        else:
            # Sequential processing
            for i in range(section_size[0]):
                visibility[i, :] = self._calculate_visibility_row(
                    i, section_size, valid_points, distances, elevation_data, observer_pos, observer_elev, v_beam_min, v_beam_max, lat_grid, lon_grid, water_handler, sw_lat, sw_lon, water_mask, observer_height, target_height, land_reflectivity, water_reflectivity, propagation_models, temperature_c, pressure_hpa, humidity_percent, rain_rate_mm_per_hour, polarization, foliage_depth_m, foliage_type
                )
                
                # Update progress if callback provided
                if progress_callback:
                    progress_callback((i + 1) / section_size[0] * 100)
        
        return visibility, {
            'north': sw_lat + 1 - start_row/pixels_per_degree,
            'south': sw_lat + 1 - end_row/pixels_per_degree,
            'east': sw_lon + end_col/pixels_per_degree,
            'west': sw_lon + start_col/pixels_per_degree
        }

    def _find_max_obstacle_height(self, observer_pos, target_pos, elevation_data, observer_elev):
        """
        Find the maximum obstacle height along the path from observer to target.
        
        Args:
            observer_pos (tuple): Observer position (row, col)
            target_pos (tuple): Target position (row, col)
            elevation_data (np.ndarray): Elevation data
            observer_elev (float): Observer elevation
            
        Returns:
            float: Maximum obstacle height above the line of sight
        """
        points = self._get_line_points(observer_pos, target_pos)
        max_obstacle_height = 0
        
        for point in points[1:-1]:  # Skip start and end points
            pr, pc = point
            if 0 <= pr < elevation_data.shape[0] and 0 <= pc < elevation_data.shape[1]:
                pelev = elevation_data[pr, pc]
                dist_to_point = np.sqrt(
                    ((pr - observer_pos[0]) * self.pixel_size)**2 +
                    ((pc - observer_pos[1]) * self.pixel_size)**2
                )
                
                # Calculate expected elevation along LOS path
                target_elev = elevation_data[target_pos[0], target_pos[1]] + self.target_height
                horizontal_distance = np.sqrt(
                    ((target_pos[0] - observer_pos[0]) * self.pixel_size)**2 +
                    ((target_pos[1] - observer_pos[1]) * self.pixel_size)**2
                )
                expected_elev = observer_elev + (target_elev - observer_elev) * (dist_to_point / horizontal_distance)
                
                # Calculate obstacle height above LOS
                obstacle_height = pelev - expected_elev
                max_obstacle_height = max(max_obstacle_height, obstacle_height)
        
        return max_obstacle_height

    def _is_angle_within_range(self, angle, start_angle, end_angle):
        """Check if an angle is within a given range (handles 0/360 wrap)."""
        if start_angle <= end_angle:
            return start_angle <= angle <= end_angle
        else:  # Range wraps around 0/360
            return angle >= start_angle or angle <= end_angle

    def _get_line_points(self, start, end):
        """Bresenham's line algorithm for points between start and end."""
        x1, y1 = start
        x2, y2 = end
        points = []
        dx = abs(x2 - x1)
        dy = abs(y2 - y1)
        x, y = x1, y1
        sx = 1 if x1 < x2 else -1
        sy = 1 if y1 < y2 else -1

        if dx > dy:
            err = dx / 2
            while x != x2:
                points.append((x, y))
                err -= dy
                if err < 0:
                    y += sy
                    err += dx
                x += sx
        else:
            err = dy / 2
            while y != y2:
                points.append((x, y))
                err -= dx
                if err < 0:
                    x += sx
                    err += dy
                y += sy
        points.append((x2, y2))
        return points

    def has_line_of_sight(self, observer_pos, target_pos, elevation_data, observer_elev, target_elev, v_beam_min, v_beam_max):
        """Check if there is a line of sight between observer and target using radar scan method with Earth curvature correction, antenna gain pattern, and Fresnel zone clearance."""
        points = self._get_line_points(observer_pos, target_pos)
        
        # Calculate vertical angle to target, adding target height to ground elevation
        target_elev = target_elev + self.target_height
        horizontal_distance = np.sqrt(
            ((target_pos[0] - observer_pos[0]) * self.pixel_size)**2 +
            ((target_pos[1] - observer_pos[1]) * self.pixel_size)**2
        )
        vertical_angle = np.degrees(np.arctan2(target_elev - observer_elev, horizontal_distance))
        
        # Check if target is within vertical beam limits
        if not (v_beam_min <= vertical_angle <= v_beam_max):
            return False
        
        # Calculate antenna gain pattern (Gaussian approximation)
        # Calculate the angular offset from boresight (assuming boresight is at 0° elevation)
        boresight_elevation = (v_beam_max + v_beam_min) / 2  # Center of beam
        angular_offset = abs(vertical_angle - boresight_elevation)
        beamwidth = v_beam_max - v_beam_min
        
        # Calculate antenna gain using Gaussian pattern
        # k = 2.776 for -3dB beamwidth (standard approximation)
        k = 2.776
        antenna_gain = np.exp(-k * (angular_offset / beamwidth)**2) if beamwidth > 0 else 1.0
        
        # Apply antenna gain threshold (e.g., -10 dB relative to peak)
        gain_threshold = 0.1  # -10 dB = 0.1 in linear scale
        if antenna_gain < gain_threshold:
            return False
        
        # Calculate wavelength for Fresnel zone calculations
        wavelength = 3e8 / (self.frequency * 1e9)  # Convert GHz to Hz, then calculate wavelength
        
        # Check for terrain obstruction with Earth curvature correction and Fresnel zone clearance
        for point in points[1:-1]:
            pr, pc = point
            if 0 <= pr < elevation_data.shape[0] and 0 <= pc < elevation_data.shape[1]:
                pelev = elevation_data[pr, pc]
                dist_to_point = np.sqrt(
                    ((pr - observer_pos[0]) * self.pixel_size)**2 +
                    ((pc - observer_pos[1]) * self.pixel_size)**2
                )
                
                # Calculate Earth's bulge at this point
                # Formula: h = (d1 * (D - d1)) / (2 * EFFECTIVE_EARTH_RADIUS)
                # where d1 is distance from observer to point, D is total distance
                bulge_height = (dist_to_point * (horizontal_distance - dist_to_point)) / (2 * self.EFFECTIVE_EARTH_RADIUS)
                
                # Calculate expected elevation along LOS path
                expected_elev = observer_elev + (target_elev - observer_elev) * (dist_to_point / horizontal_distance)
                
                # Calculate first Fresnel zone radius at this point
                # Formula: F1_radius = sqrt((wavelength * d1 * d2) / (d1 + d2))
                # where d1 is distance from observer to point, d2 is distance from point to target
                d1 = dist_to_point
                d2 = horizontal_distance - dist_to_point
                if d1 > 0 and d2 > 0:  # Avoid division by zero
                    F1_radius = np.sqrt((wavelength * d1 * d2) / (d1 + d2))
                else:
                    F1_radius = 0
                
                # Check if terrain elevation plus Earth's bulge infringes on Fresnel zone clearance
                # The obstruction condition: pelev > (expected_elev - bulge_height - (F1_radius * 0.6))
                # The 0.6 factor represents the required 60% clearance of the first Fresnel zone
                fresnel_clearance = F1_radius * 0.6
                if pelev > (expected_elev - bulge_height - fresnel_clearance + 1):  # Small tolerance for numerical precision
                    return False

        return True

    def smooth_visibility_map(self, visibility):
        """Apply minimal smoothing to maintain details while removing noise."""
        smoothed = visibility.copy()
        rows, cols = visibility.shape
        
        # Only fill single-pixel gaps
        for i in range(1, rows-1):
            for j in range(1, cols-1):
                if not visibility[i, j]:
                    # Count immediate neighbors only
                    neighbors = np.sum(visibility[i-1:i+2, j-1:j+2])
                    if neighbors >= 7:  # Must have most immediate neighbors
                        smoothed[i, j] = True
        
        return smoothed

    def get_point_at_distance(self, lat, lon, distance, angle):
        """Calculate destination point given distance and bearing from start."""
        R = 6371000  # Earth's radius in meters
        
        # Convert to radians
        lat1 = math.radians(lat)
        lon1 = math.radians(lon)
        brng = math.radians(angle)
        
        # Calculate new point
        lat2 = math.asin(
            math.sin(lat1) * math.cos(distance/R) +
            math.cos(lat1) * math.sin(distance/R) * math.cos(brng)
        )
        
        lon2 = lon1 + math.atan2(
            math.sin(brng) * math.sin(distance/R) * math.cos(lat1),
            math.cos(distance/R) - math.sin(lat1) * math.sin(lat2)
        )
        
        return math.degrees(lat2), math.degrees(lon2)

    def _get_color_for_pattern(self, pattern):
        """Get color values for different visualization patterns."""
        color_map = {
            'Red-Blue Gradient': {'rgb': (1, 0, 0), 'hex': '#ff0000', 'kml': '0000ff'},
            'Green': {'rgb': (0, 1, 0), 'hex': '#00ff00', 'kml': '00ff00'},
            'Blue': {'rgb': (0, 0, 1), 'hex': '#0000ff', 'kml': 'ff0000'},
            'Yellow': {'rgb': (1, 1, 0), 'hex': '#ffff00', 'kml': '00ffff'},
            'Purple': {'rgb': (0.5, 0, 0.5), 'hex': '#800080', 'kml': '800080'},
            'Cyan': {'rgb': (0, 1, 1), 'hex': '#00ffff', 'kml': 'ffff00'},
            'Rainbow': {'rgb': (1, 0, 0), 'hex': '#ff0000', 'kml': '0000ff'},  # Defaults to red
            'Terrain': {'rgb': (1, 0, 0), 'hex': '#ff0000', 'kml': '0000ff'}   # Defaults to red
        }
        return color_map.get(pattern, color_map['Red-Blue Gradient'])

    def export_to_kml(self, visibility, bounds, output_file, enable_rings=True, ring_interval=1, detection_distance=5, opacity=1.0, color_pattern='Red-Blue Gradient', enable_multipath_rings=False, multipath_analyzer=None, multipath_params=None):
        """Export visibility analysis as a clean KML overlay."""
        import os
        from simplekml import Kml, Color
        from skimage import measure
        
        print(f"DEBUG: export_to_kml called with output_file={output_file}")
        print(f"DEBUG: visibility shape={visibility.shape}, bounds={bounds}")
        
        # Get color for the selected pattern
        color = self._get_color_for_pattern(color_pattern)
        
        # Create output directory if needed
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Create overlay image
        overlay_file = os.path.splitext(output_file)[0] + '_overlay.png'
        plt.figure(figsize=(24, 24))
        
        # Create RGBA data based on color pattern
        rgba_data = np.zeros((*visibility.shape, 4))
        
        # Set RGB values from color pattern
        rgb_color = color['rgb']
        rgba_data[visibility, 0] = rgb_color[0]  # Red
        rgba_data[visibility, 1] = rgb_color[1]  # Green
        rgba_data[visibility, 2] = rgb_color[2]  # Blue
        rgba_data[visibility, 3] = opacity       # Alpha

        # Reduce opacity for multipath areas
        multipath_affected = visibility == False  # Or whatever condition you define
        rgba_data[multipath_affected, 3] = opacity * 0.3  # Example: Reduce opacity to 30%
        
        # Plot base visibility
        plt.imshow(rgba_data, interpolation='nearest')
        
        # Find and plot contours
        contours = measure.find_contours(visibility, 0.5)
        for contour in contours:
            plt.plot(contour[:, 1], contour[:, 0], '-', color=rgb_color, linewidth=0.5)
            
        plt.axis('off')
        plt.savefig(overlay_file,
                   dpi=600,
                   transparent=True,
                   bbox_inches='tight',
                   pad_inches=0)
        plt.close()

        # Create KML
        kml = Kml()

        # Add observer marker
        pnt = kml.newpoint(name='Observer Position')
        pnt.coords = [(self.observer_lon, self.observer_lat)]
        pnt.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/target.png'
        pnt.style.iconstyle.scale = 1.0

        # Add visibility overlay with opacity
        ground = kml.newgroundoverlay(name='Visibility Analysis')
        ground.icon.href = os.path.basename(overlay_file)
        ground.latlonbox.north = bounds['north']
        ground.latlonbox.south = bounds['south']
        ground.latlonbox.east = bounds['east']
        ground.latlonbox.west = bounds['west']
        ground.latlonbox.rotation = 0
        
        # Set the overlay opacity in KML
        alpha = int(opacity * 255)
        ground.color = f'{alpha:02x}{color["kml"]}'  # ABGR format with alpha

        # Add range rings if enabled
        if enable_rings:
            folder = kml.newfolder(name='Range Rings')
            ring_color = f'ff{color["kml"]}'  # Full opacity for rings
            
            # Create rings at specified intervals up to detection distance
            for radius in range(ring_interval, detection_distance + ring_interval, ring_interval):
                # Create circle coordinates
                circle_coords = []
                for angle in range(0, 361, 5):  # 5-degree steps
                    lat, lon = self.get_point_at_distance(
                        self.observer_lat, 
                        self.observer_lon,
                        radius * 1000,  # Convert to meters 
                        angle
                    )
                    circle_coords.append((lon, lat))
                
                # Create ring
                ring = folder.newlinestring(name=f'{radius}km')
                ring.coords = circle_coords
                ring.style.linestyle.color = ring_color
                ring.style.linestyle.width = 1
                
                # Add distance label
                label = folder.newpoint(name=f'{radius}km')
                label.coords = [(
                    circle_coords[0][0],  # Longitude
                    circle_coords[0][1]   # Latitude
                )]
                label.style.iconstyle.scale = 0
                label.style.labelstyle.scale = 0.8
                label.style.labelstyle.color = ring_color

        # Add multipath rings if enabled
        if enable_multipath_rings and multipath_analyzer and multipath_params:
            multipath_folder = kml.newfolder(name='Multipath Rings')
            
            # Calculate multipath ring polygons
            # Note: max_range_km comes from multipath_params, don't pass it explicitly
            ring_polygons = multipath_analyzer.calculate_multipath_polygons(
                center_lat=self.observer_lat,
                center_lon=self.observer_lon,
                ring_width_km=0.05,
                **multipath_params
            )
            
            for polygon in ring_polygons:
                # Convert polygon coordinates to KML format (lon, lat)
                polygon_coords = []
                for lat, lon in polygon['coordinates']:
                    polygon_coords.append((lon, lat))
                
                # Convert color from hex to KML ABGR format
                hex_color = polygon['color'].lstrip('#')
                r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                alpha = int(polygon['opacity'] * 255)
                kml_color = f'{alpha:02x}{b:02x}{g:02x}{r:02x}'
                
                # Create filled polygon for multipath ring
                mp_polygon = multipath_folder.newpolygon(
                    name=f'Multipath {polygon["distance_km"]:.1f}km ({polygon["gain_db"]:.1f}dB)'
                )
                mp_polygon.outerboundaryis = polygon_coords
                mp_polygon.style.polystyle.color = kml_color
                mp_polygon.style.polystyle.fill = 1
                mp_polygon.style.polystyle.outline = 1
                mp_polygon.style.linestyle.color = kml_color
                mp_polygon.style.linestyle.width = 1
                
                # Add description
                mp_polygon.description = (
                    f'Distance: {polygon["distance_km"]:.2f} km<br/>'
                    f'Gain: {polygon["gain_db"]:.1f} dB<br/>'
                    f'Effect: {polygon["effect_type"].title()}<br/>'
                    f'Type: {"Enhancement" if polygon["gain_db"] > 0 else "Nulling"}<br/>'
                    f'Inner Radius: {polygon["inner_radius_km"]:.3f} km<br/>'
                    f'Outer Radius: {polygon["outer_radius_km"]:.3f} km<br/>'
                    f'Variation: {polygon["variation_db"]:.1f} dB'
                )

        # Save KML
        try:
            kml.save(output_file)
            print(f"DEBUG: KML file saved successfully to {output_file}")
            print(f"DEBUG: KML file size: {os.path.getsize(output_file)} bytes")
        except Exception as e:
            print(f"ERROR: Failed to save KML file: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

        print(f"\nKML export details:")
        print(f"Observer position: {self.observer_lat:.6f}°N, {self.observer_lon:.6f}°E")
        print(f"Overlay bounds:")
        print(f"  North: {bounds['north']:.6f}°")
        print(f"  South: {bounds['south']:.6f}°")
        print(f"  East: {bounds['east']:.6f}°")
        print(f"  West: {bounds['west']:.6f}°")
        print(f"Opacity: {opacity:.2f}")
        print(f"Color Pattern: {color_pattern}")

    def visibility_to_geojson(self, visibility, bounds, color_pattern='Red-Blue Gradient', opacity=0.5, 
                             gradient_metric='distance', observer_lat=None, observer_lon=None):
        """
        Convert visibility data to GeoJSON format as a single (multi-part) polygon for the visible area.
        Uses skimage.measure.find_contours to extract the outer boundaries.
        """
        rows, cols = visibility.shape
        lat_step = (bounds['north'] - bounds['south']) / rows
        lon_step = (bounds['east'] - bounds['west']) / cols

        # Find contours at the 0.5 level (boundary between visible/invisible)
        contours = measure.find_contours(visibility, 0.5)
        polygons = []
        for contour in contours:
            # Convert contour coordinates to lat/lon
            lats = bounds['north'] - (contour[:, 0] / rows) * (bounds['north'] - bounds['south'])
            lons = bounds['west'] + (contour[:, 1] / cols) * (bounds['east'] - bounds['west'])
            coords = list(zip(lons, lats))
            # Only add valid polygons (at least 3 points)
            if len(coords) >= 3:
                polygons.append(Polygon(coords))

        # Merge polygons if possible
        if not polygons:
            # No visible area
            return geojson.FeatureCollection([])
        elif len(polygons) == 1:
            merged = polygons[0]
        else:
            # Use MultiPolygon for multiple disjoint areas
            merged = MultiPolygon(polygons)

        # Use a single color for the whole area (or could use gradient if desired)
        color = self._get_color_for_pattern(color_pattern)
        feature = geojson.Feature(
            geometry=geojson.loads(geojson.dumps(merged.__geo_interface__)),
            properties={
                'visible': True,
                'fill': color['hex'],
                'fill-opacity': opacity,
                'stroke': color['hex'],
                'stroke-width': 1,
                'stroke-opacity': opacity,
                'gradient_metric': gradient_metric
            }
        )
        return geojson.FeatureCollection([feature])

    def run_analysis(self, radar_height, target_height, max_range, h_beam_min, h_beam_max, v_beam_min, v_beam_max, frequency=10, land_reflectivity=0.3, water_reflectivity=0.9, output_file=None, use_parallel_processing=True):
        """Run the complete visibility analysis and generate outputs."""
        # Convert max_range from meters to kilometers
        radius_km = max_range / 1000
        
        # Convert from -180/180 to 0-360 system
        if h_beam_min < 0:
            h_beam_min += 360
        if h_beam_max < 0:
            h_beam_max += 360
            
        # Run visibility analysis with vertical beam parameters
        visibility, bounds = self.calculate_visibility(
            observer_height=radar_height,
            radius_km=radius_km,
            v_beam_min=v_beam_min,
            v_beam_max=v_beam_max,
            start_angle=h_beam_min,
            end_angle=h_beam_max,
            target_height=target_height,
            frequency=frequency,
            land_reflectivity=land_reflectivity,
            water_reflectivity=water_reflectivity,
            use_parallel_processing=use_parallel_processing
        )
        
        # Store the results in memory
        self.last_analysis = {
            'visibility': visibility,
            'bounds': bounds,
            'radius_km': radius_km
        }
        
        # Only create files if output_file is provided
        if output_file:
            # Plot and save the visibility map
            self.plot_2d_visibility(visibility, bounds, radius_km)
            plt.savefig(f"{output_file}_overlay.png", 
                       dpi=600,
                       transparent=True,
                       bbox_inches='tight',
                       pad_inches=0)
            plt.close()
            
            # Export to KML
            self.export_to_kml(visibility, bounds, output_file)
        
        return visibility, bounds

    def plot_2d_visibility(self, visibility, bounds, radius_km):
        """Plot the visibility map with radar-style distance rings."""

        fig = plot_2d_visibility_with_rings(
            visibility=visibility,
            bounds=bounds,
            radius_km=radius_km,
            pixel_size=self.pixel_size,
            observer_lat=self.observer_lat,
            observer_lon=self.observer_lon,
            observer_height=self.observer_height
        )

        output_file = os.path.join(os.path.dirname(self.hgt_file), 'visibility_map.png')
        fig.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()