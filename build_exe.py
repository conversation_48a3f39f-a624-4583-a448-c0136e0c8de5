#!/usr/bin/env python3
"""
Radar Analysis Tool - Standalone Executable Builder

This script creates a standalone executable (.exe) file that can run on any Windows PC
without requiring Python installation. It uses PyInstaller with optimizations for
size and performance.

Features:
- Single-file executable with all dependencies bundled
- Optimized for size and startup performance
- Includes all required data files and assets
- Handles complex dependencies (PyQt6-WebEngine, scientific libraries)
- Creates a professional installer-ready package

Usage:
    python build_exe.py [--debug] [--onefile] [--windowed]

Author: Radar Analysis Tool Development Team
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path

def check_dependencies():
    """Check if required build dependencies are installed."""
    required_packages = ['pyinstaller', 'pyinstaller-hooks-contrib']
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        for package in missing_packages:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print("✅ All dependencies installed successfully!")
    else:
        print("✅ All build dependencies are available")

def create_spec_file():
    """Create a PyInstaller spec file optimized for the Radar Analysis Tool."""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# Get the project root directory
project_root = Path(os.path.abspath('.'))

# Define data files to include
data_files = [
    # Configuration files
    ('config.example.json', '.'),
    ('CONFIGURATION.md', '.'),
    ('README.md', '.'),
    ('SIMULATION_GUIDE.md', '.'),
    
    # Assets
    ('assets/radar_icon.png', 'assets'),
    ('assets/radar_icon1.png', 'assets'),
    
    # SRTM data directory (empty folder structure)
    ('srtm_data', 'srtm_data'),
    
    # Output directories
    ('output', 'output'),
    ('exports', 'exports'),
]

# Define hidden imports for complex dependencies
hidden_imports = [
    # PyQt6 components
    'PyQt6.QtCore',
    'PyQt6.QtWidgets',
    'PyQt6.QtGui',
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'PyQt6.QtWebChannel',
    'PyQt6.QtNetwork',
    
    # Scientific computing
    'numpy',
    'scipy',
    'scipy.spatial',
    'scipy.spatial.transform',
    'scipy.optimize',
    'scipy.interpolate',
    'scipy.ndimage',
    'scipy.signal',
    
    # Image processing
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'skimage',
    'skimage.measure',
    'skimage.filters',
    'skimage.transform',
    
    # Geospatial libraries
    'rasterio',
    'rasterio.warp',
    'rasterio.transform',
    'shapely',
    'shapely.geometry',
    'shapely.ops',
    'geojson',
    'folium',
    'folium.plugins',
    'simplekml',
    
    # Matplotlib for plotting
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.figure',
    'matplotlib.axes',
    
    # Custom modules
    'interface',
    'interface.main_window',
    'interface.settings_dialog',
    'interface.about_dialog',
    'interface.worker',
    'interface.control_panel',
    'interface.visualization',
    
    'elevation',
    'elevation.srtm_handler',
    'elevation.srtm_verification_dialog',
    'elevation.srtm_downloader',
    'elevation.elevation_google',
    'elevation.elevation_srtm',
    'elevation.google_elevation',
    'elevation.water_mask_handler',
    
    'utils',
    'utils.color_utils',
    'utils.constants',
    'utils.coordinate_utils',
    'utils.distance_calculator',
    'utils.multipath_analyzer',
    'utils.propagation_models',
    
    'viewshed',
    'viewshed.calculator',
    'viewshed.optimization',
    'viewshed.renderer',
    
    'mapping',
    'mapping.map_provider',
    'mapping.map_renderer',
    'mapping.map_utils',
    
    # Core modules
    'visibility_2d',
    'plotting_utils',
    'smoothing_utils',
]

# Define excluded modules to reduce size
excludes = [
    'tkinter',
    'test',
    'unittest',
    'distutils',
    'setuptools',
    'pkg_resources',
    'IPython',
    'jupyter',
    'notebook',
    'pandas',
    'seaborn',
    'plotly',
    'bokeh',
    'dash',
    'flask',
    'django',
    'sqlalchemy',
    'sqlite3',
    'pymongo',
    'redis',
    'celery',
    'requests',
    'urllib3',
    'certifi',
    'charset_normalizer',
    'idna',
    'chardet',
    'PyQt5',
    'PySide2',
    'PySide6',
]

# Define binary files to include
binaries = []

# Define datas with proper paths
datas = []
for src, dst in data_files:
    src_path = project_root / src
    if src_path.exists():
        if src_path.is_file():
            datas.append((str(src_path), dst))
        elif src_path.is_dir():
            # For directories, include all files recursively
            for file_path in src_path.rglob('*'):
                if file_path.is_file():
                    rel_path = file_path.relative_to(src_path)
                    datas.append((str(file_path), f'{dst}/{rel_path.parent}'))

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Remove duplicate files
a.datas = list(set(a.datas))

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='Radar_Analysis_Tool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # Set to True for debug builds
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/radar_icon.ico' if os.path.exists('assets/radar_icon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Radar_Analysis_Tool',
)
'''
    
    with open('Radar_Analysis_Tool.spec', 'w') as f:
        f.write(spec_content)
    
    print("✅ PyInstaller spec file created: Radar_Analysis_Tool.spec")

def create_icon_file():
    """Create an .ico file from the existing PNG icon if it doesn't exist."""
    icon_path = Path('assets/radar_icon.ico')
    png_path = Path('assets/radar_icon.png')
    
    if not icon_path.exists() and png_path.exists():
        try:
            from PIL import Image
            img = Image.open(png_path)
            # Convert to RGBA if not already
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Create multiple sizes for the icon
            sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
            img.save(icon_path, format='ICO', sizes=sizes)
            print(f"✅ Created icon file: {icon_path}")
        except Exception as e:
            print(f"⚠️  Could not create icon file: {e}")
    elif icon_path.exists():
        print(f"✅ Icon file already exists: {icon_path}")
    else:
        print(f"⚠️  No icon source found: {png_path}")

def build_executable(debug=False, onefile=False, windowed=True):
    """Build the executable using PyInstaller."""
    
    print("🔨 Starting executable build process...")
    
    # Create spec file
    create_spec_file()
    
    # Create icon file
    create_icon_file()
    
    # Build command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',  # Clean cache before building
        '--noconfirm',  # Replace existing build without asking
    ]
    
    # Only add options if NOT using a .spec file
    use_spec = True  # Always use .spec for this project
    if use_spec:
        cmd.append('Radar_Analysis_Tool.spec')
        print("📝 Using .spec file for build (single-file and windowed mode set in .spec)")
    else:
        if debug:
            cmd.extend(['--debug', 'all'])
            print("🐛 Debug mode enabled")
        if onefile:
            cmd.append('--onefile')
            print("📦 Single-file mode enabled")
        else:
            cmd.append('--onedir')
            print("📁 Directory mode enabled")
        if windowed and not debug:
            cmd.append('--windowed')
            print("🪟 Windowed mode enabled")
        # Add optimization flags
        cmd.extend([
            '--optimize', '2',  # Python optimization level
            '--strip',  # Strip debug symbols
        ])
        cmd.append('main.py')
    
    print(f"🚀 Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed with error code {e.returncode}")
        print(f"Error output: {e.stderr}")
        return False

def create_installer_script():
    """Create a simple installer script for the executable."""
    
    installer_content = '''@echo off
echo Radar Analysis Tool - Installer
echo ================================
echo.

REM Create installation directory
set INSTALL_DIR=%PROGRAMFILES%\\Radar_Analysis_Tool
echo Creating installation directory: %INSTALL_DIR%
mkdir "%INSTALL_DIR%" 2>nul

REM Copy files
echo Copying application files...
xcopy /E /I /Y "dist\\Radar_Analysis_Tool" "%INSTALL_DIR%"

REM Create desktop shortcut
echo Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\\Desktop
set SHORTCUT=%DESKTOP%\\Radar Analysis Tool.lnk

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Radar_Analysis_Tool.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\\assets\\radar_icon.ico'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
set START_MENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs
mkdir "%START_MENU%\\Radar Analysis Tool" 2>nul

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\\Radar Analysis Tool\\Radar Analysis Tool.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Radar_Analysis_Tool.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\\assets\\radar_icon.ico'; $Shortcut.Save()"

echo.
echo Installation completed successfully!
echo The Radar Analysis Tool is now available in:
echo - Desktop shortcut
echo - Start Menu
echo - %INSTALL_DIR%
echo.
pause
'''
    
    with open('install_radar_tool.bat', 'w') as f:
        f.write(installer_content)
    
    print("✅ Installer script created: install_radar_tool.bat")

def create_readme():
    """Create a README file for the executable distribution."""
    
    readme_content = '''# Radar Analysis Tool - Standalone Executable

## Overview
This is a standalone executable version of the Radar Analysis Tool that can run on any Windows PC without requiring Python installation.

## Features
- **No Python Required**: Runs independently on Windows systems
- **Complete Functionality**: All features of the original application included
- **Professional Interface**: Full PyQt6-based user interface
- **Advanced Analysis**: Radar coverage, multipath analysis, and terrain modeling
- **Export Capabilities**: KML, CSV, and GeoJSON export options

## System Requirements
- **Operating System**: Windows 10 or later (64-bit)
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 500MB free space
- **Graphics**: DirectX 11 compatible graphics card
- **Internet**: Required for map tiles and SRTM data download

## Installation

### Option 1: Simple Installation
1. Run `install_radar_tool.bat` as Administrator
2. Follow the installation prompts
3. Launch from Desktop shortcut or Start Menu

### Option 2: Manual Installation
1. Extract the `Radar_Analysis_Tool` folder to your desired location
2. Run `Radar_Analysis_Tool.exe` directly
3. Create shortcuts as needed

## Usage
1. **Launch**: Double-click `Radar_Analysis_Tool.exe`
2. **Set Location**: Enter latitude/longitude and click "Go to Location"
3. **Configure Parameters**: Adjust radar and analysis settings
4. **Run Simulation**: Click "Run Simulation" to analyze coverage
5. **Export Results**: Use File menu to export in various formats

## File Structure
```
Radar_Analysis_Tool/
├── Radar_Analysis_Tool.exe    # Main executable
├── assets/                    # Application icons and resources
├── srtm_data/                 # Elevation data cache
├── output/                    # Simulation output files
├── exports/                   # Export files
└── [other dependencies]       # Required libraries and modules
```

## Troubleshooting

### Common Issues

**Application won't start:**
- Ensure you have Windows 10 or later
- Check that antivirus software isn't blocking the executable
- Try running as Administrator

**Map not loading:**
- Check internet connection
- Verify firewall settings allow the application
- Try different map providers in Settings

**Slow performance:**
- Close other applications to free memory
- Reduce analysis range or resolution
- Check available disk space

**SRTM data download issues:**
- Verify internet connection
- Check NASA Earthdata credentials in Settings
- Ensure sufficient disk space for elevation data

### Getting Help
- Check the application's built-in help system
- Review the configuration documentation
- Contact support with detailed error messages

## Technical Details
- **Build Tool**: PyInstaller
- **Python Version**: 3.8+
- **Framework**: PyQt6
- **Dependencies**: NumPy, SciPy, Matplotlib, Folium, Rasterio, Shapely

## License
This software is provided as-is for educational and research purposes.

## Version
Built on: {build_date}
Python: {python_version}
PyInstaller: {pyinstaller_version}
'''.format(
        build_date=subprocess.check_output(['powershell', '-Command', 'Get-Date -Format "yyyy-MM-dd"']).decode().strip(),
        python_version=sys.version.split()[0],
        pyinstaller_version=subprocess.check_output([sys.executable, '-m', 'pip', 'show', 'pyinstaller']).decode().split('Version: ')[1].split('\n')[0]
    )
    
    with open('EXECUTABLE_README.md', 'w') as f:
        f.write(readme_content)
    
    print("✅ Executable README created: EXECUTABLE_README.md")

def build_executable_webengine():
    """Build the executable with proper PyQt6 WebEngine support."""
    
    print("🔨 Starting WebEngine-optimized executable build process...")
    
    # Create icon file
    create_icon_file()
    
    # Build command with comprehensive WebEngine support
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',  # Single file executable
        '--windowed',  # No console window
        '--clean',  # Clean cache
        '--noconfirm',  # Replace existing build
        '--name', 'Radar_Analysis_Tool',
        '--distpath', 'dist',
        '--workpath', 'build',
        '--specpath', '.',
        
        # Add icon if available
        '--icon', 'assets/radar_icon.ico' if os.path.exists('assets/radar_icon.ico') else None,
        
        # Critical PyQt6 WebEngine components
        '--hidden-import', 'PyQt6.QtWebEngineWidgets',
        '--hidden-import', 'PyQt6.QtWebEngineCore', 
        '--hidden-import', 'PyQt6.QtWebChannel',
        '--hidden-import', 'PyQt6.QtWebEngineQuick',
        '--hidden-import', 'PyQt6.QtWebSockets',
        '--hidden-import', 'PyQt6.QtPrintSupport',
        
        # Core PyQt6 components
        '--hidden-import', 'PyQt6.QtCore',
        '--hidden-import', 'PyQt6.QtWidgets',
        '--hidden-import', 'PyQt6.QtGui',
        '--hidden-import', 'PyQt6.QtNetwork',
        '--hidden-import', 'PyQt6.QtOpenGL',
        '--hidden-import', 'PyQt6.QtOpenGLWidgets',
        
        # Scientific computing
        '--hidden-import', 'numpy',
        '--hidden-import', 'scipy',
        '--hidden-import', 'scipy.spatial',
        '--hidden-import', 'scipy.spatial.transform',
        '--hidden-import', 'scipy.optimize',
        '--hidden-import', 'scipy.interpolate',
        '--hidden-import', 'scipy.ndimage',
        '--hidden-import', 'scipy.signal',
        
        # Visualization libraries
        '--hidden-import', 'matplotlib',
        '--hidden-import', 'matplotlib.pyplot',
        '--hidden-import', 'matplotlib.backends.backend_qt5agg',
        '--hidden-import', 'matplotlib.backends.backend_agg',
        '--hidden-import', 'matplotlib.figure',
        '--hidden-import', 'matplotlib.axes',
        
        # Image processing
        '--hidden-import', 'PIL',
        '--hidden-import', 'PIL.Image',
        '--hidden-import', 'PIL.ImageDraw',
        '--hidden-import', 'skimage',
        '--hidden-import', 'skimage.measure',
        '--hidden-import', 'skimage.filters',
        '--hidden-import', 'skimage.transform',
        
        # Geospatial libraries
        '--hidden-import', 'rasterio',
        '--hidden-import', 'rasterio.warp',
        '--hidden-import', 'rasterio.transform',
        '--hidden-import', 'rasterio.features',
        '--hidden-import', 'rasterio.mask',
        '--hidden-import', 'shapely',
        '--hidden-import', 'shapely.geometry',
        '--hidden-import', 'shapely.ops',
        '--hidden-import', 'geojson',
        '--hidden-import', 'folium',
        '--hidden-import', 'folium.plugins',
        '--hidden-import', 'simplekml',
        
        # Application modules
        '--hidden-import', 'interface',
        '--hidden-import', 'interface.main_window',
        '--hidden-import', 'interface.settings_dialog',
        '--hidden-import', 'interface.about_dialog',
        '--hidden-import', 'interface.worker',
        '--hidden-import', 'interface.control_panel',
        '--hidden-import', 'interface.visualization',
        '--hidden-import', 'elevation',
        '--hidden-import', 'elevation.srtm_handler',
        '--hidden-import', 'elevation.srtm_verification_dialog',
        '--hidden-import', 'elevation.srtm_downloader',
        '--hidden-import', 'elevation.elevation_google',
        '--hidden-import', 'elevation.elevation_srtm',
        '--hidden-import', 'elevation.google_elevation',
        '--hidden-import', 'elevation.water_mask_handler',
        '--hidden-import', 'utils',
        '--hidden-import', 'utils.color_utils',
        '--hidden-import', 'utils.constants',
        '--hidden-import', 'utils.coordinate_utils',
        '--hidden-import', 'utils.distance_calculator',
        '--hidden-import', 'utils.multipath_analyzer',
        '--hidden-import', 'utils.propagation_models',
        '--hidden-import', 'viewshed',
        '--hidden-import', 'viewshed.calculator',
        '--hidden-import', 'viewshed.optimization',
        '--hidden-import', 'viewshed.renderer',
        '--hidden-import', 'mapping',
        '--hidden-import', 'mapping.map_provider',
        '--hidden-import', 'mapping.map_renderer',
        '--hidden-import', 'mapping.map_utils',
        '--hidden-import', 'visibility_2d',
        '--hidden-import', 'plotting_utils',
        '--hidden-import', 'smoothing_utils',
        
        # Add essential data files
        '--add-data', 'config.example.json;.',
        '--add-data', 'assets;assets',
        '--add-data', 'srtm_data;srtm_data',
        '--add-data', 'output;output',
        '--add-data', 'exports;exports',
        
        # Exclude problematic modules
        '--exclude-module', 'distutils',
        '--exclude-module', 'setuptools',
        '--exclude-module', 'pkg_resources',
        '--exclude-module', 'tkinter',
        '--exclude-module', 'test',
        '--exclude-module', 'unittest',
        '--exclude-module', 'IPython',
        '--exclude-module', 'jupyter',
        '--exclude-module', 'notebook',
        '--exclude-module', 'pandas',
        '--exclude-module', 'seaborn',
        '--exclude-module', 'plotly',
        '--exclude-module', 'bokeh',
        '--exclude-module', 'PyQt5',
        '--exclude-module', 'PySide2',
        '--exclude-module', 'PySide6',
        
        # Main script
        'main.py'
    ]
    
    # Remove None values from the command
    cmd = [item for item in cmd if item is not None]
    
    print(f"🚀 Running WebEngine build command...")
    print(f"Command length: {len(cmd)} arguments")
    
    try:
        # Set environment variables for better WebEngine support
        env = os.environ.copy()
        env['QTWEBENGINE_DISABLE_SANDBOX'] = '1'
        env['QTWEBENGINE_CHROMIUM_FLAGS'] = '--disable-gpu-sandbox --no-sandbox'
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, env=env)
        print("✅ WebEngine build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ WebEngine build failed with error code {e.returncode}")
        print(f"Error output: {e.stderr}")
        if e.stdout:
            print(f"Standard output: {e.stdout}")
        return False

def create_webengine_spec():
    """Create a comprehensive spec file for WebEngine support."""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# Get the project root directory
project_root = Path(os.path.abspath('.'))

# Define data files to include
data_files = [
    # Configuration files
    ('config.example.json', '.'),
    ('CONFIGURATION.md', '.'),
    ('README.md', '.'),
    ('SIMULATION_GUIDE.md', '.'),
    
    # Assets
    ('assets/radar_icon.png', 'assets'),
    ('assets/radar_icon1.png', 'assets'),
    ('assets/radar_icon.ico', 'assets'),
    
    # Data directories
    ('srtm_data', 'srtm_data'),
    ('output', 'output'),
    ('exports', 'exports'),
]

# Critical hidden imports for WebEngine
hidden_imports = [
    # PyQt6 WebEngine - CRITICAL
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'PyQt6.QtWebChannel',
    'PyQt6.QtWebEngineQuick',
    'PyQt6.QtWebSockets',
    'PyQt6.QtPrintSupport',
    
    # Core PyQt6 components
    'PyQt6.QtCore',
    'PyQt6.QtWidgets',
    'PyQt6.QtGui',
    'PyQt6.QtNetwork',
    'PyQt6.QtOpenGL',
    'PyQt6.QtOpenGLWidgets',
    
    # Scientific computing
    'numpy',
    'scipy',
    'scipy.spatial',
    'scipy.spatial.transform',
    'scipy.optimize',
    'scipy.interpolate',
    'scipy.ndimage',
    'scipy.signal',
    
    # Image processing
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'skimage',
    'skimage.measure',
    'skimage.filters',
    'skimage.transform',
    
    # Geospatial libraries
    'rasterio',
    'rasterio.warp',
    'rasterio.transform',
    'rasterio.features',
    'rasterio.mask',
    'shapely',
    'shapely.geometry',
    'shapely.ops',
    'geojson',
    'folium',
    'folium.plugins',
    'simplekml',
    
    # Matplotlib for plotting
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_agg',
    'matplotlib.figure',
    'matplotlib.axes',
    
    # Custom modules
    'interface',
    'interface.main_window',
    'interface.settings_dialog',
    'interface.about_dialog',
    'interface.worker',
    'interface.control_panel',
    'interface.visualization',
    
    'elevation',
    'elevation.srtm_handler',
    'elevation.srtm_verification_dialog',
    'elevation.srtm_downloader',
    'elevation.elevation_google',
    'elevation.elevation_srtm',
    'elevation.google_elevation',
    'elevation.water_mask_handler',
    
    'utils',
    'utils.color_utils',
    'utils.constants',
    'utils.coordinate_utils',
    'utils.distance_calculator',
    'utils.multipath_analyzer',
    'utils.propagation_models',
    
    'viewshed',
    'viewshed.calculator',
    'viewshed.optimization',
    'viewshed.renderer',
    
    'mapping',
    'mapping.map_provider',
    'mapping.map_renderer',
    'mapping.map_utils',
    
    # Core modules
    'visibility_2d',
    'plotting_utils',
    'smoothing_utils',
]

# Define excluded modules to reduce size
excludes = [
    'tkinter',
    'test',
    'unittest',
    'distutils',
    'setuptools',
    'pkg_resources',
    'IPython',
    'jupyter',
    'notebook',
    'pandas',
    'seaborn',
    'plotly',
    'bokeh',
    'dash',
    'flask',
    'django',
    'sqlalchemy',
    'sqlite3',
    'pymongo',
    'redis',
    'celery',
    'PyQt5',
    'PySide2',
    'PySide6',
]

# Define binary files to include
binaries = []

# Define datas with proper paths
datas = []
for src, dst in data_files:
    src_path = project_root / src
    if src_path.exists():
        if src_path.is_file():
            datas.append((str(src_path), dst))
        elif src_path.is_dir():
            # For directories, include all files recursively
            for file_path in src_path.rglob('*'):
                if file_path.is_file():
                    rel_path = file_path.relative_to(src_path)
                    datas.append((str(file_path), f'{dst}/{rel_path.parent}'))

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Remove duplicate files
a.datas = list(set(a.datas))

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Radar_Analysis_Tool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/radar_icon.ico' if os.path.exists('assets/radar_icon.ico') else None,
)
'''
    
    with open('Radar_Analysis_Tool_WebEngine.spec', 'w') as f:
        f.write(spec_content)
    
    print("✅ WebEngine spec file created: Radar_Analysis_Tool_WebEngine.spec")

def main():
    """Main build function."""
    parser = argparse.ArgumentParser(description='Build Radar Analysis Tool executable')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--webengine', action='store_true', default=True, help='Use WebEngine-optimized build (default)')
    parser.add_argument('--spec', action='store_true', help='Use spec file approach')
    parser.add_argument('--webengine-spec', action='store_true', help='Use WebEngine spec file approach')
    
    args = parser.parse_args()
    
    print("🚀 Radar Analysis Tool - Executable Builder")
    print("=" * 50)
    
    # Check dependencies
    check_dependencies()
    
    # Choose build approach
    if args.webengine_spec:
        # Use the WebEngine spec file approach
        create_webengine_spec()
        success = build_executable(
            debug=args.debug,
            onefile=True,
            windowed=True
        )
    elif args.spec:
        # Use the original spec file approach
        success = build_executable(
            debug=args.debug,
            onefile=True,
            windowed=True
        )
    else:
        # Use the WebEngine-optimized approach (default)
        success = build_executable_webengine()
    
    if success:
        # Create additional files
        create_installer_script()
        create_readme()
        
        print("\n🎉 Build completed successfully!")
        print("\n📁 Output files:")
        print("   - dist/Radar_Analysis_Tool.exe (single executable)")
        print("   - install_radar_tool.bat (installer script)")
        print("   - EXECUTABLE_README.md (documentation)")
        
        print("\n📋 Next steps:")
        print("   1. Test the executable on a clean system")
        print("   2. Create a distribution package")
        print("   3. Run the installer script as Administrator")
        
        # Show file sizes
        if os.path.exists('dist/Radar_Analysis_Tool.exe'):
            size = os.path.getsize('dist/Radar_Analysis_Tool.exe') / (1024 * 1024)
            print(f"\n📦 Executable size: {size:.1f} MB")
    else:
        print("\n❌ Build failed. Check the error messages above.")
        sys.exit(1)

if __name__ == '__main__':
    main() 