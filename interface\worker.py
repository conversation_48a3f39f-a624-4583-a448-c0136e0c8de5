# file: interface/worker.py
from PyQt6.QtCore import QObject, pyqtSignal
import traceback
import sys

class Worker(QObject):
    """
    A generic worker object for running tasks in a separate QThread.
    """
    finished = pyqtSignal(object)  # Emits the result object when done
    error = pyqtSignal(tuple)      # Emits (exception_type, exception_object, traceback)
    progress = pyqtSignal(int)     # Emits progress percentage (0-100)

    def __init__(self, function, *args, **kwargs):
        super().__init__()
        self.function = function
        self.args = args
        self.kwargs = kwargs

    def run(self):
        """Execute the worker's task."""
        try:
            # The function to run should accept a 'progress_callback' keyword argument
            self.kwargs['progress_callback'] = self.progress.emit
            result = self.function(*self.args, **self.kwargs)
        except Exception as e:
            ex_type, ex_value, ex_traceback = sys.exc_info()
            self.error.emit((ex_type, ex_value, traceback.format_exc()))
        else:
            self.finished.emit(result) 