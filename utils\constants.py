import os

# Directories
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
RESOURCES_DIR = os.path.join(BASE_DIR, 'resources')
ICONS_DIR = os.path.join(RESOURCES_DIR, 'icons')
STYLES_DIR = os.path.join(RESOURCES_DIR, 'styles')
SRTM_DATA_DIR = os.path.join(BASE_DIR, 'srtm_data')
EXPORT_DIR = os.path.join(BASE_DIR, 'exports')

# SRTM Data
SRTM_BASE_URL = "https://dwtkns.com/srtm30m"
SRTM_AUTH_URL = "https://e4ftl01.cr.usgs.gov/MEASURES/SRTMGL1.003/2000.02.11"

# Google Elevation API
GOOGLE_ELEVATION_API_URL = "https://maps.googleapis.com/maps/api/elevation/json"

# Default Settings
DEFAULT_SETTINGS_FILE = os.path.join(BASE_DIR, 'settings.json')
DEFAULT_COLOR_SCHEME = {
    'clear': 'red',
    'partial': 'yellow',
    'edge': 'blue',
    'no_visibility': 'grey'
}

DEFAULT_HORIZONTAL_BEAM_ANGLES = ["90", "180", "270", "360"]
MAP_PROVIDERS = ["OpenStreetMap", "Stamen Terrain", "Stamen Toner", "CartoDB Positron", "CartoDB DarkMatter"]