"""
Advanced RF Propagation Models

This module implements various RF propagation models for more accurate
signal strength predictions in radar and radio communications.

Models included:
- Knife-Edge Diffraction (ITU-R P.526)
- Atmospheric Absorption (ITU-R P.676)
- Rain Fading (ITU-R P.838)
- Foliage Loss (ITU-R P.833)
"""

import numpy as np
import math

class PropagationModels:
    """Collection of RF propagation models for enhanced accuracy."""
    
    def __init__(self):
        """Initialize propagation models."""
        pass
    
    def knife_edge_diffraction_loss(self, frequency_hz, distance_m, obstacle_height_m, 
                                   radar_height_m, target_height_m):
        """
        Calculate knife-edge diffraction loss using ITU-R P.526.
        
        Args:
            frequency_hz (float): Frequency in Hz
            distance_m (float): Total distance between radar and target
            obstacle_height_m (float): Height of the obstacle above ground
            radar_height_m (float): Height of radar above ground
            target_height_m (float): Height of target above ground
            
        Returns:
            float: Diffraction loss in dB
        """
        # Calculate the Fresnel-Kirchhoff diffraction parameter
        wavelength = 3e8 / frequency_hz
        
        # Calculate the clearance parameter
        # h = obstacle height relative to the line joining radar and target
        radar_target_line_height = radar_height_m + (target_height_m - radar_height_m) * 0.5
        clearance = obstacle_height_m - radar_target_line_height
        
        # Calculate distances from obstacle to radar and target
        # For simplicity, assume obstacle is at midpoint
        d1 = d2 = distance_m / 2
        
        # Calculate the diffraction parameter
        v = clearance * np.sqrt(2 * (d1 + d2) / (wavelength * d1 * d2))
        
        # Calculate diffraction loss using ITU-R P.526 approximation
        if v <= -0.78:
            # Deep shadow region
            loss_db = 0
        elif v <= 0:
            # Shadow region
            loss_db = 6.9 + 20 * np.log10(np.sqrt((v - 0.1)**2 + 1) + v - 0.1)
        elif v <= 1:
            # Transition region
            loss_db = 6.02 + 9.11 * v - 1.27 * v**2
        else:
            # Illuminated region
            loss_db = 12.953 + 4.343 * np.log10(v)
        
        return max(0, loss_db)  # Ensure non-negative loss
    
    def atmospheric_absorption_loss(self, frequency_hz, distance_m, temperature_c=15, 
                                  pressure_hpa=1013.25, humidity_percent=60):
        """
        Calculate atmospheric absorption loss using ITU-R P.676.
        
        Args:
            frequency_hz (float): Frequency in Hz
            distance_m (float): Distance in meters
            temperature_c (float): Temperature in Celsius
            pressure_hpa (float): Atmospheric pressure in hPa
            humidity_percent (float): Relative humidity in percent
            
        Returns:
            float: Atmospheric absorption loss in dB
        """
        frequency_ghz = frequency_hz / 1e9
        
        # Convert to Kelvin
        temperature_k = temperature_c + 273.15
        
        # Calculate water vapor density (g/m³)
        # Using simplified formula from ITU-R P.676
        water_vapor_density = humidity_percent * 0.01 * 6.11 * np.exp(17.27 * temperature_c / (temperature_c + 237.3)) / (temperature_k * 0.4615)
        
        # Simplified atmospheric absorption model
        # This is a basic approximation - full ITU-R P.676 is more complex
        
        # Oxygen absorption (simplified)
        gamma_o = 0.0
        if 50 <= frequency_ghz <= 70:
            gamma_o = 0.1 * (frequency_ghz - 50) / 20
        elif 70 < frequency_ghz <= 120:
            gamma_o = 0.1 + 0.2 * (frequency_ghz - 70) / 50
        
        # Water vapor absorption (simplified)
        gamma_w = 0.0
        if 20 <= frequency_ghz <= 40:
            gamma_w = 0.05 * water_vapor_density * (frequency_ghz - 20) / 20
        elif 40 < frequency_ghz <= 100:
            gamma_w = 0.05 * water_vapor_density * (1 + 0.5 * (frequency_ghz - 40) / 60)
        
        # Total absorption coefficient (dB/km)
        gamma_total = gamma_o + gamma_w
        
        # Convert distance to km and calculate total loss
        distance_km = distance_m / 1000
        absorption_loss = gamma_total * distance_km
        
        return absorption_loss
    
    def rain_fading_loss(self, frequency_hz, distance_m, rain_rate_mm_per_hour, 
                        polarization='horizontal'):
        """
        Calculate rain fading loss using ITU-R P.838.
        
        Args:
            frequency_hz (float): Frequency in Hz
            distance_m (float): Distance in meters
            rain_rate_mm_per_hour (float): Rain rate in mm/hour
            polarization (str): Polarization ('horizontal' or 'vertical')
            
        Returns:
            float: Rain fading loss in dB
        """
        frequency_ghz = frequency_hz / 1e9
        
        # ITU-R P.838 coefficients for specific attenuation
        if polarization == 'horizontal':
            k_h = 0.0001424 * frequency_ghz**1.6
            alpha_h = 1.31
        else:  # vertical
            k_v = 0.0001291 * frequency_ghz**1.6
            alpha_v = 1.31
            k_h, alpha_h = k_v, alpha_v
        
        # Calculate specific attenuation (dB/km)
        specific_attenuation = k_h * (rain_rate_mm_per_hour ** alpha_h)
        
        # Calculate effective path length
        # For distances < 22 km, use actual distance
        distance_km = distance_m / 1000
        if distance_km <= 22:
            effective_distance = distance_km
        else:
            # For longer distances, use effective path length
            effective_distance = 22 * (1 + 0.1 * np.log10(distance_km / 22))
        
        # Calculate total rain fading loss
        rain_loss = specific_attenuation * effective_distance
        
        return rain_loss
    
    def foliage_loss(self, frequency_hz, distance_m, foliage_depth_m, 
                    foliage_type='dense'):
        """
        Calculate foliage loss using ITU-R P.833.
        
        Args:
            frequency_hz (float): Frequency in Hz
            distance_m (float): Distance through foliage in meters
            foliage_depth_m (float): Depth of foliage in meters
            foliage_type (str): Type of foliage ('sparse', 'medium', 'dense')
            
        Returns:
            float: Foliage loss in dB
        """
        frequency_ghz = frequency_hz / 1e9
        
        # Foliage loss coefficients based on ITU-R P.833
        if foliage_type == 'sparse':
            a = 0.2
            b = 0.3
        elif foliage_type == 'medium':
            a = 0.4
            b = 0.6
        else:  # dense
            a = 0.6
            b = 0.9
        
        # Calculate specific foliage loss (dB/m)
        specific_loss = a * frequency_ghz**b
        
        # Calculate total foliage loss
        foliage_loss = specific_loss * foliage_depth_m
        
        return foliage_loss
    
    def calculate_total_propagation_loss(self, frequency_hz, distance_m, 
                                       radar_height_m, target_height_m,
                                       obstacle_height_m=None, temperature_c=15,
                                       pressure_hpa=1013.25, humidity_percent=60,
                                       rain_rate_mm_per_hour=0, polarization='horizontal',
                                       foliage_depth_m=0, foliage_type='dense'):
        """
        Calculate total propagation loss including all effects.
        
        Args:
            frequency_hz (float): Frequency in Hz
            distance_m (float): Distance in meters
            radar_height_m (float): Radar height above ground
            target_height_m (float): Target height above ground
            obstacle_height_m (float): Height of obstacle (if any)
            temperature_c (float): Temperature in Celsius
            pressure_hpa (float): Atmospheric pressure in hPa
            humidity_percent (float): Relative humidity in percent
            rain_rate_mm_per_hour (float): Rain rate in mm/hour
            polarization (str): Polarization
            foliage_depth_m (float): Depth of foliage
            foliage_type (str): Type of foliage
            
        Returns:
            dict: Dictionary containing all loss components and total loss
        """
        losses = {}
        
        # Free space path loss (basic)
        wavelength = 3e8 / frequency_hz
        free_space_loss = 20 * np.log10(4 * np.pi * distance_m / wavelength)
        losses['free_space'] = free_space_loss
        
        # Atmospheric absorption
        if humidity_percent > 0:
            losses['atmospheric'] = self.atmospheric_absorption_loss(
                frequency_hz, distance_m, temperature_c, pressure_hpa, humidity_percent
            )
        else:
            losses['atmospheric'] = 0
        
        # Rain fading
        if rain_rate_mm_per_hour > 0:
            losses['rain'] = self.rain_fading_loss(
                frequency_hz, distance_m, rain_rate_mm_per_hour, polarization
            )
        else:
            losses['rain'] = 0
        
        # Foliage loss
        if foliage_depth_m > 0:
            losses['foliage'] = self.foliage_loss(
                frequency_hz, distance_m, foliage_depth_m, foliage_type
            )
        else:
            losses['foliage'] = 0
        
        # Diffraction loss
        if obstacle_height_m is not None and obstacle_height_m > 0:
            losses['diffraction'] = self.knife_edge_diffraction_loss(
                frequency_hz, distance_m, obstacle_height_m, radar_height_m, target_height_m
            )
        else:
            losses['diffraction'] = 0
        
        # Calculate total loss
        total_loss = sum(losses.values())
        losses['total'] = total_loss
        
        return losses 