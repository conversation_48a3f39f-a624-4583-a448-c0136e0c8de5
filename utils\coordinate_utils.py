import os

"""
Utility functions for coordinate conversions and handling.
Supports both decimal and DMS (Degrees, Minutes, Seconds) formats.
"""

def dms_to_decimal(degrees, minutes, seconds, direction):
    """Convert DMS (degrees-minutes-seconds) to decimal degrees"""
    decimal = float(degrees) + float(minutes)/60 + float(seconds)/(60*60)
    if direction in ['S', 'W']:
        decimal = -decimal
    return decimal

def decimal_to_dms(decimal, is_latitude=True):
    """Convert decimal degrees to DMS (degrees-minutes-seconds)"""
    direction = 'N' if is_latitude else 'E'
    if decimal < 0:
        direction = 'S' if is_latitude else 'W'
        decimal = abs(decimal)
    
    degrees = int(decimal)
    decimal_minutes = (decimal - degrees) * 60
    minutes = int(decimal_minutes)
    seconds = round((decimal_minutes - minutes) * 60, 2)
    
    return degrees, minutes, seconds, direction

def parse_dms_string(dms_str):
    """
    Parse a DMS string into decimal degrees
    Accepts formats like:
    - '50°31'58.55" N'
    - '3°30'40.41" W'
    """
    # Remove special characters and split
    parts = dms_str.replace('°', ' ').replace('\'', ' ').replace('"', ' ').strip().split()
    
    if len(parts) == 4:  # Full DMS format
        try:
            degrees = float(parts[0])
            minutes = float(parts[1])
            seconds = float(parts[2])
            direction = parts[3].upper()
            
            if direction not in ['N', 'S', 'E', 'W']:
                raise ValueError("Invalid direction. Must be N, S, E, or W")
                
            return dms_to_decimal(degrees, minutes, seconds, direction)
        except (ValueError, IndexError) as e:
            raise ValueError(f"Invalid DMS string format: {e}")
    else:
        raise ValueError("Invalid DMS string format. Expected format: DD°MM'SS.SS\" D")

def format_dms_string(degrees, minutes, seconds, direction):
    """
    Format DMS components into a string
    Example: 50°31'58.55" N
    """
    return f"{int(degrees)}°{int(minutes)}'{seconds:.2f}\" {direction}"

def get_coords_from_filename(filename):
    """
    Extract coordinates from SRTM .hgt filename.
    SRTM files are named as e.g., 'N50E006.hgt' where the coordinates
    represent the southwest corner of the tile.
    
    Args:
        filename (str): Name of the SRTM file
    
    Returns:
        tuple: (latitude, longitude) of the southwest corner in decimal degrees
    """
    basename = os.path.basename(filename)
    if not basename.endswith('.hgt'):
        raise ValueError("Not a valid HGT file")
        
    # Extract the coordinates from the filename
    lat_dir = basename[0]
    lat = int(basename[1:3])
    lon_dir = basename[3]
    lon = int(basename[4:7])
    
    # Convert to decimal degrees
    lat = lat if lat_dir == 'N' else -lat
    lon = lon if lon_dir == 'E' else -lon
    
    return lat, lon

def get_srtm_bounds(sw_lat, sw_lon):
    """
    Calculate the bounds of an SRTM tile given its southwest corner coordinates.
    Each SRTM tile covers exactly one degree in both latitude and longitude.
    
    Args:
        sw_lat (float): Southwest corner latitude
        sw_lon (float): Southwest corner longitude
    
    Returns:
        tuple: (north, south, east, west) bounds of the tile
    """
    return (
        sw_lat + 1.0,  # north bound (add 1 degree to south)
        sw_lat,        # south bound (same as input lat)
        sw_lon + 1.0,  # east bound (add 1 degree to west)
        sw_lon         # west bound (same as input lon)
    )