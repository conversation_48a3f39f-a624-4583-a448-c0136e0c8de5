# file: elevation/water_mask_handler.py (New File)
import numpy as np
import os

class WaterMaskHandler:
    """
    Handler for SRTM Water Body Data (SWBD) to determine water vs land surfaces.
    This enables differentiated multipath reflectivity calculations.
    """
    
    def __init__(self, water_mask_dir='srtm_data'):
        """
        Initialize the water mask handler.
        
        Args:
            water_mask_dir (str): Directory containing SRTM water body data files
        """
        self.water_mask_dir = water_mask_dir
        self.cache = {}
        
    def get_water_mask_for_tile(self, hgt_filename):
        """
        Loads and caches a water mask for a given SRTM tile.
        
        Args:
            hgt_filename (str): Name of the HGT file (e.g., 'N50W003.hgt')
            
        Returns:
            np.ndarray: Boolean mask where True indicates water, False indicates land
        """
        if hgt_filename in self.cache:
            return self.cache[hgt_filename]
        
        # SWBD files might have different naming conventions
        # Try different possible water mask file formats
        water_mask_paths = [
            # Standard SWBD format
            os.path.join(self.water_mask_dir, hgt_filename.replace('.hgt', '_water.hgt')),
            # Alternative formats
            os.path.join(self.water_mask_dir, 'water_masks', hgt_filename),
            os.path.join(self.water_mask_dir, hgt_filename.replace('.hgt', '.wbd')),
        ]
        
        mask = None
        for mask_path in water_mask_paths:
            if os.path.exists(mask_path):
                try:
                    mask = self._load_water_mask_file(mask_path)
                    break
                except Exception as e:
                    print(f"Warning: Could not load water mask from {mask_path}: {e}")
                    continue
        
        if mask is None:
            # Return a mask of all land if no water data is available
            print(f"Warning: No water mask found for {hgt_filename}, assuming all land")
            mask = np.zeros((3601, 3601), dtype=bool)
        
        self.cache[hgt_filename] = mask
        return mask
    
    def _load_water_mask_file(self, mask_path):
        """
        Load a water mask file.
        
        Args:
            mask_path (str): Path to the water mask file
            
        Returns:
            np.ndarray: Boolean mask where True indicates water
        """
        with open(mask_path, 'rb') as f:
            # The format is typically 1 byte per pixel, 0=land, 1=water
            # SRTM files are 3601x3601 pixels
            data = np.fromfile(f, dtype=np.uint8, count=3601*3601)
            
            if len(data) != 3601*3601:
                raise ValueError(f"Invalid water mask file size: expected {3601*3601}, got {len(data)}")
            
            mask = data.reshape((3601, 3601)) == 1
            return mask
    
    def is_water(self, lat, lon, sw_lat, sw_lon, water_mask):
        """
        Checks if a specific coordinate is on water.
        
        Args:
            lat (float): Latitude of the point
            lon (float): Longitude of the point
            sw_lat (float): Southwest latitude of the HGT tile
            sw_lon (float): Southwest longitude of the HGT tile
            water_mask (np.ndarray): Water mask for the tile
            
        Returns:
            bool: True if the point is on water, False if on land
        """
        # SRTM data has 3600 pixels per degree
        pixels_per_degree = 3600
        
        # Calculate row and column indices in the water mask
        # Note: SRTM files are arranged with north at top, so we need to flip the row calculation
        row = int(round((sw_lat + 1 - lat) * pixels_per_degree))
        col = int(round((lon - sw_lon) * pixels_per_degree))
        
        # Check bounds
        if 0 <= row < water_mask.shape[0] and 0 <= col < water_mask.shape[1]:
            return water_mask[row, col]
        
        # If out of bounds, assume land
        return False
    
    def get_point_at_distance(self, start_lat, start_lon, distance_m, bearing_deg):
        """
        Calculate a point at a given distance and bearing from a starting point.
        
        Args:
            start_lat (float): Starting latitude in degrees
            start_lon (float): Starting longitude in degrees
            distance_m (float): Distance in meters
            bearing_deg (float): Bearing in degrees (0-360)
            
        Returns:
            tuple: (lat, lon) of the destination point
        """
        import math
        
        # Earth's radius in meters
        R = 6371000
        
        # Convert to radians
        lat1 = math.radians(start_lat)
        lon1 = math.radians(start_lon)
        bearing = math.radians(bearing_deg)
        
        # Calculate destination point
        lat2 = math.asin(math.sin(lat1) * math.cos(distance_m/R) + 
                        math.cos(lat1) * math.sin(distance_m/R) * math.cos(bearing))
        
        lon2 = lon1 + math.atan2(math.sin(bearing) * math.sin(distance_m/R) * math.cos(lat1),
                                math.cos(distance_m/R) - math.sin(lat1) * math.sin(lat2))
        
        # Convert back to degrees
        return math.degrees(lat2), math.degrees(lon2)
    
    def get_bearing(self, lat1, lon1, lat2, lon2):
        """
        Calculate the bearing from point 1 to point 2.
        
        Args:
            lat1, lon1 (float): First point coordinates
            lat2, lon2 (float): Second point coordinates
            
        Returns:
            float: Bearing in degrees (0-360)
        """
        import math
        
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        dlon = lon2 - lon1
        
        y = math.sin(dlon) * math.cos(lat2)
        x = math.cos(lat1) * math.sin(lat2) - math.sin(lat1) * math.cos(lat2) * math.cos(dlon)
        
        bearing = math.atan2(y, x)
        
        # Convert to degrees and normalize to 0-360
        bearing = math.degrees(bearing)
        bearing = (bearing + 360) % 360
        
        return bearing
    
    def get_reflection_point(self, radar_lat, radar_lon, target_lat, target_lon, 
                           radar_height, target_height):
        """
        Calculate the reflection point for multipath analysis with Earth curvature correction.
        
        Args:
            radar_lat, radar_lon (float): Radar coordinates
            target_lat, target_lon (float): Target coordinates
            radar_height (float): Radar height above ground
            target_height (float): Target height above ground
            
        Returns:
            tuple: (reflection_lat, reflection_lon) of the reflection point
        """
        import math
        
        # Earth's radius in meters
        R = 6371000
        
        # Calculate total distance between radar and target
        total_distance = self._calculate_distance(radar_lat, radar_lon, target_lat, target_lon)
        
        # For Earth curvature corrected reflection, we need to account for the curved surface
        # The reflection point location depends on the relative heights and Earth's curvature
        
        # Calculate the angle subtended by the path at Earth's center
        central_angle = total_distance / R
        
        # Calculate the effective heights including Earth's curvature
        # The effective height is the height above the curved Earth surface
        effective_radar_height = radar_height + R * (1 - math.cos(central_angle/2))
        effective_target_height = target_height + R * (1 - math.cos(central_angle/2))
        
        # Calculate reflection point using curved Earth geometry
        # The reflection point is where the angle of incidence equals angle of reflection
        # For a curved surface, this is approximately at the midpoint for equal heights
        # For different heights, it's weighted toward the lower antenna
        
        total_effective_height = effective_radar_height + effective_target_height
        if total_effective_height > 0:
            # Weight the reflection point based on effective heights
            reflection_ratio = effective_radar_height / total_effective_height
        else:
            reflection_ratio = 0.5
        
        # Calculate reflection distance from radar
        reflection_distance = total_distance * reflection_ratio
        
        # Calculate bearing from radar to target
        bearing = self.get_bearing(radar_lat, radar_lon, target_lat, target_lon)
        
        # Calculate reflection point using great circle navigation
        reflection_lat, reflection_lon = self.get_point_at_distance(
            radar_lat, radar_lon, reflection_distance, bearing
        )
        
        return reflection_lat, reflection_lon
    
    def _calculate_distance(self, lat1, lon1, lat2, lon2):
        """
        Calculate the great circle distance between two points.
        
        Args:
            lat1, lon1, lat2, lon2 (float): Coordinates of the two points
            
        Returns:
            float: Distance in meters
        """
        import math
        
        # Earth's radius in meters
        R = 6371000
        
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c
        
        return distance
    
    def clear_cache(self):
        """Clear the water mask cache."""
        self.cache.clear()
    
    def get_cache_info(self):
        """Get information about the current cache."""
        return {
            'cached_tiles': list(self.cache.keys()),
            'cache_size': len(self.cache),
            'memory_usage_mb': sum(mask.nbytes for mask in self.cache.values()) / (1024*1024)
        } 