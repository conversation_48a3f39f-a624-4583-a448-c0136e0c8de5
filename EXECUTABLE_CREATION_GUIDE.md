# Radar Analysis Tool - Executable Creation Guide

## Overview

This guide provides comprehensive instructions for creating standalone executable files from the Radar Analysis Tool. Due to compatibility issues with Python 3.13, this document outlines the challenges and provides multiple alternative solutions.

## Current Situation

### ⚠️ Python 3.13 Compatibility Issues

The current development environment uses **Python 3.13**, which has known compatibility issues with major executable creation tools:

- **PyInstaller**: Fails with `distutils` module conflicts and circular import errors
- **cx_Freeze**: Experiences recursion depth errors during module scanning
- **Root Cause**: Python 3.13 introduced changes to the packaging ecosystem that break dependency analysis

### ✅ Application Status

The Radar Analysis Tool itself is **fully functional** and ready for deployment:
- ✅ All core features working correctly
- ✅ Dependencies installed and validated
- ✅ Comprehensive test suite confirms application health
- ✅ Professional-grade functionality with advanced features

## Quick Start (Recommended)

### Option 1: Use Python 3.11/3.12 Environment

**This is the most straightforward solution:**

```bash
# Install Python 3.11 or 3.12 alongside your current Python
# Create a new virtual environment
python3.11 -m venv venv_build

# Activate the environment (Windows)
venv_build\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install pyinstaller

# Build the executable
python build_exe.py --simple
```

### Option 2: Use the Test Suite

Run the diagnostic test to understand your specific situation:

```bash
python "Test Files/test_executable_build.py"
```

This will:
- Analyze your Python version compatibility
- Test PyInstaller functionality
- Validate all dependencies
- Provide specific recommendations
- Create alternative build scripts

## Alternative Solutions

### 1. 🐍 Python Version Management

**Install Python 3.11 or 3.12:**

#### Windows:
1. Download Python 3.11/3.12 from python.org
2. Install alongside existing Python
3. Create virtual environment:
   ```bash
   py -3.11 -m venv venv_build
   venv_build\Scripts\activate
   pip install -r requirements.txt pyinstaller
   python build_exe.py
   ```

#### Linux/macOS:
```bash
# Using pyenv (recommended)
pyenv install 3.11.7
pyenv virtualenv 3.11.7 radar-build
pyenv activate radar-build
pip install -r requirements.txt pyinstaller
python build_exe.py
```

### 2. 🐳 Docker Containerization

Create a Docker container with compatible Python:

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements.txt pyinstaller
RUN python build_exe.py

# Extract the built executable
VOLUME ["/app/dist"]
```

Build and extract:
```bash
docker build -t radar-build .
docker run -v "$(pwd)/dist:/app/dist" radar-build
```

### 3. 📦 cx_Freeze Alternative

Use the provided cx_Freeze setup:

```bash
pip install cx_Freeze
python setup_cx_freeze.py build
```

**Note**: cx_Freeze may also have Python 3.13 issues, but can work in some cases.

### 4. 🔧 Nuitka Compilation

Compile Python to standalone executable:

```bash
pip install nuitka
python -m nuitka --standalone --enable-plugin=pyqt6 main.py
```

### 5. 🏃 PyOxidizer (Advanced)

Use Rust-based Python packaging:

1. Install Rust: https://rustup.rs/
2. Install PyOxidizer: `cargo install pyoxidizer`
3. Initialize project: `pyoxidizer init-rust-project radar-tool`
4. Configure `pyoxidizer.bzl` for your application
5. Build: `pyoxidizer build`

### 6. 📱 Auto-py-to-exe (GUI Approach)

Use a graphical interface for PyInstaller:

```bash
pip install auto-py-to-exe
auto-py-to-exe
```

Configure through the GUI:
- Script: `main.py`
- Additional Files: `assets/`, `config.example.json`
- Hidden Imports: All PyQt6, numpy, scipy modules

## Build Scripts Available

### 1. Enhanced PyInstaller (`build_exe.py`)

```bash
# Simple approach (default)
python build_exe.py --simple

# Complex spec file approach
python build_exe.py --spec

# Debug mode
python build_exe.py --simple --debug
```

### 2. cx_Freeze Setup (`setup_cx_freeze.py`)

```bash
python setup_cx_freeze.py build
```

### 3. Test and Diagnostic (`Test Files/test_executable_build.py`)

```bash
python "Test Files/test_executable_build.py"
```

## Troubleshooting

### Common Issues

#### PyInstaller Fails with "distutils" Error
- **Cause**: Python 3.13 compatibility issue
- **Solution**: Use Python 3.11 or 3.12

#### cx_Freeze Recursion Error
- **Cause**: Complex dependency scanning on Python 3.13
- **Solution**: Simplify package list or use different Python version

#### Missing Dependencies in Executable
- **Cause**: Hidden imports not detected
- **Solution**: Add to `--hidden-import` list or spec file

#### Large Executable Size
- **Cause**: All dependencies included
- **Solution**: Use `--exclude-module` for unused packages

### Validation Steps

1. **Test the Application First**:
   ```bash
   python main.py
   ```

2. **Run Health Check**:
   ```bash
   python "Test Files/test_application_health.py"
   ```

3. **Validate Dependencies**:
   ```bash
   python "Test Files/test_executable_build.py"
   ```

4. **Test Executable**:
   - Run on clean system without Python
   - Check all features work correctly
   - Verify data files are included

## Output Files

### Successful Build Produces:

```
dist/
├── Radar_Analysis_Tool.exe          # Main executable
├── [dependencies...]                # Required libraries (if --onedir)

build/                               # Temporary build files
├── [build artifacts]

Additional Files:
├── install_radar_tool.bat          # Windows installer script
├── EXECUTABLE_README.md             # End-user documentation
└── [build logs]                    # Error diagnostics
```

### File Sizes

- **Single File Executable**: ~150-300 MB
- **Directory Distribution**: ~200-400 MB
- **Compressed**: ~50-100 MB

## System Requirements

### Build Requirements:
- **Python**: 3.11 or 3.12 (recommended)
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Disk**: 2GB free space for build process
- **OS**: Windows 10+, Linux, macOS

### Runtime Requirements (for executable):
- **OS**: Windows 10+ (64-bit)
- **Memory**: 2GB RAM minimum
- **Disk**: 500MB free space
- **Graphics**: DirectX 11 compatible
- **Network**: Internet for map tiles and SRTM data

## Professional Deployment

### For Distribution:

1. **Test on Clean System**:
   - Windows VM without Python
   - Different user accounts
   - Various Windows versions

2. **Create Installer**:
   ```bash
   # Use provided installer script
   install_radar_tool.bat
   
   # Or create MSI installer
   pip install cx_Freeze
   python setup.py bdist_msi
   ```

3. **Digital Signing** (optional):
   - Code signing certificate
   - Sign executable for trusted deployment

4. **Documentation**:
   - Include EXECUTABLE_README.md
   - User manual
   - Configuration guide

## Support

### Getting Help

1. **Check Test Results**:
   ```bash
   python "Test Files/test_executable_build.py"
   ```

2. **Review Build Logs**:
   - Check console output
   - Examine build/ directory
   - Look for error messages

3. **Common Solutions**:
   - Use compatible Python version
   - Install missing dependencies
   - Check file permissions
   - Verify antivirus exclusions

### Known Working Configurations

| Python Version | PyInstaller | cx_Freeze | Status |
|---------------|-------------|-----------|---------|
| 3.13.x        | ❌          | ❌        | Issues |
| 3.12.x        | ✅          | ✅        | Works |
| 3.11.x        | ✅          | ✅        | Works |
| 3.10.x        | ✅          | ✅        | Works |

## Future Updates

### Monitoring for Fixes

- **PyInstaller**: Monitor releases for Python 3.13 support
- **cx_Freeze**: Watch for compatibility updates
- **Python**: Track packaging ecosystem improvements

### Update Strategy

1. Test new tool versions with current codebase
2. Update build scripts as needed
3. Validate on multiple systems
4. Update documentation

---

## Quick Reference

### One-Command Solutions

```bash
# Most Compatible (Python 3.11/3.12)
python build_exe.py --simple

# Alternative Tool
python setup_cx_freeze.py build

# Diagnostic Test
python "Test Files/test_executable_build.py"

# Docker Build
docker build -t radar-build . && docker run -v "$(pwd)/dist:/app/dist" radar-build
```

### Emergency Workaround

If all else fails, you can distribute the application as a Python package:

```bash
# Create portable Python distribution
pip install -r requirements.txt --target portable_python/
# Include Python interpreter
# Create launch script: python main.py
```

This guide ensures you have multiple paths to create a deployable executable, with clear documentation of the current limitations and comprehensive alternatives. 