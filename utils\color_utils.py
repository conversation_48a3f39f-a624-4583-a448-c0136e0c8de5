"""
Color Utilities Module for Radar Analysis Tool

This module provides consistent color management across the application,
including true color gradients, conversions between color formats,
and standardized color schemes for viewshed visualization.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from typing import Dict, List, Tuple, Union, Optional


class ColorUtils:
    """Centralized color management for the radar analysis application."""
    
    # Standard color schemes for viewshed visualization
    COLOR_SCHEMES = {
        'Red-Blue Gradient': {
            'type': 'gradient',
            'colors': ['#0000ff', '#00ffff', '#00ff00', '#ffff00', '#ff0000'],
            'description': 'Blue (low) to Red (high) gradient'
        },
        'Rainbow': {
            'type': 'gradient',
            'colors': ['#ff0000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff'],
            'description': 'Full spectrum rainbow gradient'
        },
        'Terrain': {
            'type': 'gradient',
            'colors': ['#8B4513', '#A0522D', '#CD853F', '#DEB887', '#F5DEB3', '#90EE90', '#228B22', '#006400'],
            'description': 'Terrain elevation color scheme'
        },
        'Heat': {
            'type': 'gradient',
            'colors': ['#000000', '#800000', '#ff0000', '#ff8000', '#ffff00', '#ffffff'],
            'description': 'Black to white heat map'
        },
        'Viridis': {
            'type': 'gradient',
            'colors': ['#440154', '#31688E', '#35B779', '#FDE725'],
            'description': 'Viridis colorblind-friendly gradient'
        },
        'Plasma': {
            'type': 'gradient',
            'colors': ['#0D0887', '#7E03A8', '#CC4778', '#F89441', '#F0F921'],
            'description': 'Plasma colorblind-friendly gradient'
        },
        'Green': {
            'type': 'solid',
            'color': '#00ff00',
            'description': 'Solid green color'
        },
        'Blue': {
            'type': 'solid',
            'color': '#0000ff',
            'description': 'Solid blue color'
        },
        'Yellow': {
            'type': 'solid',
            'color': '#ffff00',
            'description': 'Solid yellow color'
        },
        'Purple': {
            'type': 'solid',
            'color': '#800080',
            'description': 'Solid purple color'
        },
        'Cyan': {
            'type': 'solid',
            'color': '#00ffff',
            'description': 'Solid cyan color'
        },
        'Red': {
            'type': 'solid',
            'color': '#ff0000',
            'description': 'Solid red color'
        }
    }
    
    @staticmethod
    def get_color_scheme_info(pattern_name: str) -> Dict:
        """
        Get information about a color scheme.
        
        Args:
            pattern_name: Name of the color scheme
            
        Returns:
            Dictionary containing scheme information
        """
        return ColorUtils.COLOR_SCHEMES.get(pattern_name, ColorUtils.COLOR_SCHEMES['Red-Blue Gradient'])
    
    @staticmethod
    def get_color_hex(pattern_name: str, value: float = 0.5, min_val: float = 0.0, max_val: float = 1.0) -> str:
        """
        Get hex color for a pattern and value.
        
        Args:
            pattern_name: Name of the color scheme
            value: Value to map to color (0.0 to 1.0)
            min_val: Minimum value for normalization
            max_val: Maximum value for normalization
            
        Returns:
            Hex color string (e.g., '#ff0000')
        """
        scheme = ColorUtils.get_color_scheme_info(pattern_name)
        
        if scheme['type'] == 'solid':
            return scheme['color']
        
        # Normalize value to 0-1 range
        normalized_value = np.clip((value - min_val) / (max_val - min_val), 0.0, 1.0)
        
        # Create colormap from gradient colors
        colors = scheme['colors']
        cmap = mcolors.LinearSegmentedColormap.from_list(pattern_name, colors)
        
        # Get color at normalized value
        rgba = cmap(normalized_value)
        hex_color = mcolors.to_hex(rgba)
        
        return hex_color
    
    @staticmethod
    def get_color_rgb(pattern_name: str, value: float = 0.5, min_val: float = 0.0, max_val: float = 1.0) -> Tuple[float, float, float]:
        """
        Get RGB color tuple for a pattern and value.
        
        Args:
            pattern_name: Name of the color scheme
            value: Value to map to color (0.0 to 1.0)
            min_val: Minimum value for normalization
            max_val: Maximum value for normalization
            
        Returns:
            RGB tuple (r, g, b) with values 0.0 to 1.0
        """
        scheme = ColorUtils.get_color_scheme_info(pattern_name)
        
        if scheme['type'] == 'solid':
            hex_color = scheme['color']
            return mcolors.to_rgb(hex_color)
        
        # Normalize value to 0-1 range
        normalized_value = np.clip((value - min_val) / (max_val - min_val), 0.0, 1.0)
        
        # Create colormap from gradient colors
        colors = scheme['colors']
        cmap = mcolors.LinearSegmentedColormap.from_list(pattern_name, colors)
        
        # Get color at normalized value
        rgba = cmap(normalized_value)
        return rgba[:3]  # Return RGB only
    
    @staticmethod
    def hex_to_kml_abgr(hex_color: str, alpha: float = 1.0) -> str:
        """
        Convert hex color to KML ABGR format.
        
        Args:
            hex_color: Hex color string (e.g., '#ff0000')
            alpha: Alpha value (0.0 to 1.0)
            
        Returns:
            KML ABGR color string (e.g., 'ff0000ff')
        """
        # Remove '#' if present
        hex_color = hex_color.lstrip('#')
        
        # Parse RGB values
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        
        # Convert alpha to 0-255 range
        a = int(alpha * 255)
        
        # Return in ABGR format
        return f'{a:02x}{b:02x}{g:02x}{r:02x}'
    
    @staticmethod
    def rgb_to_hex(r: float, g: float, b: float) -> str:
        """
        Convert RGB values to hex color.
        
        Args:
            r: Red value (0.0 to 1.0)
            g: Green value (0.0 to 1.0)
            b: Blue value (0.0 to 1.0)
            
        Returns:
            Hex color string
        """
        return mcolors.to_hex((r, g, b))
    
    @staticmethod
    def create_gradient_colors(pattern_name: str, num_colors: int = 256) -> List[str]:
        """
        Create a list of gradient colors for a pattern.
        
        Args:
            pattern_name: Name of the color scheme
            num_colors: Number of colors to generate
            
        Returns:
            List of hex color strings
        """
        scheme = ColorUtils.get_color_scheme_info(pattern_name)
        
        if scheme['type'] == 'solid':
            return [scheme['color']] * num_colors
        
        # Create colormap from gradient colors
        colors = scheme['colors']
        cmap = mcolors.LinearSegmentedColormap.from_list(pattern_name, colors)
        
        # Generate colors
        hex_colors = []
        for i in range(num_colors):
            value = i / (num_colors - 1)
            rgba = cmap(value)
            hex_colors.append(mcolors.to_hex(rgba))
        
        return hex_colors
    
    @staticmethod
    def get_available_patterns() -> List[str]:
        """
        Get list of available color patterns.
        
        Returns:
            List of pattern names
        """
        return list(ColorUtils.COLOR_SCHEMES.keys())
    
    @staticmethod
    def get_pattern_descriptions() -> Dict[str, str]:
        """
        Get descriptions for all color patterns.
        
        Returns:
            Dictionary mapping pattern names to descriptions
        """
        return {name: scheme['description'] for name, scheme in ColorUtils.COLOR_SCHEMES.items()}
    
    @staticmethod
    def create_legend_image(pattern_name: str, width: int = 200, height: int = 20) -> np.ndarray:
        """
        Create a legend image for a color pattern.
        
        Args:
            pattern_name: Name of the color scheme
            width: Width of the legend image in pixels
            height: Height of the legend image in pixels
            
        Returns:
            RGBA image array
        """
        scheme = ColorUtils.get_color_scheme_info(pattern_name)
        
        if scheme['type'] == 'solid':
            # Create solid color image
            hex_color = scheme['color']
            rgb = mcolors.to_rgb(hex_color)
            image = np.zeros((height, width, 4))
            image[:, :, 0] = rgb[0]  # Red
            image[:, :, 1] = rgb[1]  # Green
            image[:, :, 2] = rgb[2]  # Blue
            image[:, :, 3] = 1.0     # Alpha
            return image
        
        # Create gradient image
        colors = scheme['colors']
        cmap = mcolors.LinearSegmentedColormap.from_list(pattern_name, colors)
        
        # Generate gradient
        x = np.linspace(0, 1, width)
        image = np.zeros((height, width, 4))
        
        for i, val in enumerate(x):
            rgba = cmap(val)
            image[:, i, :] = rgba
        
        return image
    
    @staticmethod
    def save_legend_image(pattern_name: str, filename: str, width: int = 200, height: int = 20, dpi: int = 100):
        """
        Save a legend image for a color pattern to file.
        
        Args:
            pattern_name: Name of the color scheme
            filename: Output filename
            width: Width of the legend image in pixels
            height: Height of the legend image in pixels
            dpi: DPI for the output image
        """
        image = ColorUtils.create_legend_image(pattern_name, width, height)
        
        fig, ax = plt.subplots(figsize=(width/dpi, height/dpi), dpi=dpi)
        ax.imshow(image)
        ax.axis('off')
        plt.tight_layout()
        plt.savefig(filename, dpi=dpi, bbox_inches='tight', pad_inches=0)
        plt.close()


# Convenience functions for backward compatibility
def get_color_hex(pattern_name: str, value: float = 0.5, min_val: float = 0.0, max_val: float = 1.0) -> str:
    """Convenience function for getting hex color."""
    return ColorUtils.get_color_hex(pattern_name, value, min_val, max_val)


def get_color_rgb(pattern_name: str, value: float = 0.5, min_val: float = 0.0, max_val: float = 1.0) -> Tuple[float, float, float]:
    """Convenience function for getting RGB color."""
    return ColorUtils.get_color_rgb(pattern_name, value, min_val, max_val)


def hex_to_kml_abgr(hex_color: str, alpha: float = 1.0) -> str:
    """Convenience function for hex to KML ABGR conversion."""
    return ColorUtils.hex_to_kml_abgr(hex_color, alpha)


def get_available_patterns() -> List[str]:
    """Convenience function for getting available patterns."""
    return ColorUtils.get_available_patterns() 