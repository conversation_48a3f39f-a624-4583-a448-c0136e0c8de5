"""
Multipath Analyzer Module
Provides FMCW radar multipath interference analysis and visualization.
"""

import numpy as np
import matplotlib.pyplot as plt
import math

# Try PyQt6 first, then fallback to PyQt5
try:
    from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
except ImportError:
    try:
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    except ImportError:
        # Fallback for testing without Qt
        FigureCanvas = None

from matplotlib.figure import Figure

class MultipathAnalyzer:
    """
    Class for multipath interference analysis and visualization.
    """
    
    def __init__(self, frequency=16e9):
        """
        Initialize the multipath analyzer.
        
        Args:
            frequency (float): Radar frequency in Hz
        """
        self.frequency = frequency
        self.c = 3e8  # Speed of light
        self.wavelength = self.c / self.frequency
        self.max_distance = 30000  # 30 km default
        self.max_height = 40  # 40 m default
        
        # Analysis parameters
        self.radar_height = 15.0  # meters
        self.target_height = 40.0  # meters
        self.reflectivity = 50  # percent
        
        # Distance array for calculations
        self.distances = np.linspace(1, self.max_distance, 3000)
        
        # Store last calculation
        self.last_gain_pattern = None
        self.last_parameters = None
    
    def set_frequency(self, frequency_ghz):
        """
        Set the radar frequency.
        
        Args:
            frequency_ghz (float): Frequency in GHz
        """
        self.frequency = frequency_ghz * 1e9
        self.wavelength = self.c / self.frequency
    
    def set_geometry(self, radar_height, target_height, reflectivity_percent):
        """
        Set the radar geometry parameters.
        
        Args:
            radar_height (float): Radar height in meters
            target_height (float): Target height in meters
            reflectivity_percent (float): Ground reflectivity percentage (0-100)
        """
        self.radar_height = radar_height
        self.target_height = target_height
        self.reflectivity = reflectivity_percent
    
    def set_analysis_range(self, max_distance_km):
        """
        Set the maximum analysis distance.
        
        Args:
            max_distance_km (float): Maximum distance in kilometers
        """
        self.max_distance = max_distance_km * 1000
        self.distances = np.linspace(1, self.max_distance, 3000)
    
    def calculate_multipath_gain(self, distances=None, radar_height=None, 
                               target_height=None, reflectivity_percent=None):
        """
        Calculate multipath gain pattern.
        
        Args:
            distances (array): Distance array in meters (optional)
            radar_height (float): Radar height in meters (optional)
            target_height (float): Target height in meters (optional)
            reflectivity_percent (float): Reflectivity percentage (optional)
        
        Returns:
            tuple: (distances_km, gain_db) arrays
        """
        # Use provided parameters or defaults
        if distances is None:
            distances = self.distances
        if radar_height is None:
            radar_height = self.radar_height
        if target_height is None:
            target_height = self.target_height
        if reflectivity_percent is None:
            reflectivity_percent = self.reflectivity
        
        # Convert reflectivity percentage to complex coefficient with phase inversion
        gamma = -reflectivity_percent / 100  # Negative for phase inversion
        
        # Calculate path lengths with protection against negative values
        direct_path = np.sqrt(np.maximum(0, distances**2 + (target_height - radar_height)**2))
        reflected_path = np.sqrt(np.maximum(0, distances**2 + (target_height + radar_height)**2))
        
        # Calculate phase differences (including path difference and reflection phase)
        phase_diff = (2 * np.pi / self.wavelength) * (reflected_path - direct_path)
        
        # Calculate field components
        with np.errstate(divide='ignore'):
            E_direct = 1 / direct_path
            E_reflected = gamma / reflected_path * np.exp(-1j * phase_diff)
        
        # Total field and power ratio
        E_total = E_direct + E_reflected
        power_ratio = np.abs(E_total)**2 / (E_direct**2)
        
        # Handle numerical instability at very short ranges
        power_ratio[np.isinf(power_ratio)] = 1
        power_ratio = np.nan_to_num(power_ratio, nan=1)
        
        # Convert to dB
        gain_db = 10 * np.log10(power_ratio)
        
        # Store results
        self.last_gain_pattern = gain_db
        self.last_parameters = {
            'radar_height': radar_height,
            'target_height': target_height,
            'reflectivity': reflectivity_percent,
            'frequency_ghz': self.frequency / 1e9,
            'max_distance_km': self.max_distance / 1000
        }
        
        return distances / 1000, gain_db  # Return distances in km
    
    def find_multipath_nulls(self, threshold_db=-20):
        """
        Find multipath null locations where interference is destructive.
        
        Args:
            threshold_db (float): Threshold in dB below which to consider as null
        
        Returns:
            list: List of distances (km) where nulls occur
        """
        if self.last_gain_pattern is None:
            self.calculate_multipath_gain()
        
        # Find minima below threshold
        gain = self.last_gain_pattern
        distances_km = self.distances / 1000
        
        # Find local minima
        minima_indices = []
        for i in range(1, len(gain) - 1):
            if gain[i] < gain[i-1] and gain[i] < gain[i+1] and gain[i] < threshold_db:
                minima_indices.append(i)
        
        null_distances = [distances_km[i] for i in minima_indices]
        
        return null_distances
    
    def find_multipath_peaks(self, threshold_db=0):
        """
        Find multipath peak locations where interference is constructive.
        
        Args:
            threshold_db (float): Threshold in dB above which to consider as peak
        
        Returns:
            list: List of distances (km) where peaks occur
        """
        if self.last_gain_pattern is None:
            self.calculate_multipath_gain()
        
        # Find maxima above threshold
        gain = self.last_gain_pattern
        distances_km = self.distances / 1000
        
        # Find local maxima
        maxima_indices = []
        for i in range(1, len(gain) - 1):
            if gain[i] > gain[i-1] and gain[i] > gain[i+1] and gain[i] > threshold_db:
                maxima_indices.append(i)
        
        peak_distances = [distances_km[i] for i in maxima_indices]
        
        return peak_distances
    
    def get_multipath_summary(self):
        """
        Get a summary of multipath analysis results.
        
        Returns:
            dict: Summary of multipath analysis
        """
        if self.last_gain_pattern is None:
            self.calculate_multipath_gain()
        
        distances_km, gain_db = self.distances / 1000, self.last_gain_pattern
        
        # Calculate statistics
        max_gain = np.max(gain_db)
        min_gain = np.min(gain_db)
        mean_gain = np.mean(gain_db)
        std_gain = np.std(gain_db)
        
        # Find nulls and peaks
        nulls = self.find_multipath_nulls()
        peaks = self.find_multipath_peaks()
        
        summary = {
            'parameters': self.last_parameters,
            'statistics': {
                'max_gain_db': max_gain,
                'min_gain_db': min_gain,
                'mean_gain_db': mean_gain,
                'std_gain_db': std_gain,
                'dynamic_range_db': max_gain - min_gain
            },
            'nulls_km': nulls,
            'peaks_km': peaks,
            'null_count': len(nulls),
            'peak_count': len(peaks)
        }
        
        return summary
    
    def calculate_multipath_rings(self, center_lat, center_lon, max_range_km, 
                                effect_threshold_db=-10, color_scheme='Rainbow (dB-based)',
                                opacity=0.6, show_nulls_only=False, show_peaks_only=False,
                                smooth_transitions=True):
        """
        Calculate multipath ring positions and strengths for map visualization.
        
        Args:
            center_lat (float): Radar center latitude
            center_lon (float): Radar center longitude
            max_range_km (float): Maximum range in km
            effect_threshold_db (float): Threshold for significant multipath effect
            color_scheme (str): Color scheme for rings
            opacity (float): Ring opacity (0-1)
            show_nulls_only (bool): Show only nulls
            show_peaks_only (bool): Show only peaks
            smooth_transitions (bool): Use smooth transitions instead of discrete rings
        
        Returns:
            list: List of ring dictionaries with position and strength data
        """
        if self.last_gain_pattern is None:
            self.calculate_multipath_gain()
        
        distances_km = self.distances / 1000
        gain_db = self.last_gain_pattern
        
        # Find significant multipath effects
        rings = []
        
        # Use different sampling based on smooth_transitions
        step_size = 10 if smooth_transitions else 50
        
        for i in range(0, len(gain_db) - 1, step_size):
            if distances_km[i] > max_range_km:
                break
            
            # Calculate local variation and gain
            window_size = min(step_size, len(gain_db) - i)
            local_gain = gain_db[i:i+window_size]
            local_variation = np.std(local_gain)
            avg_gain = np.mean(local_gain)
            
            # Apply threshold filtering
            if abs(avg_gain) < abs(effect_threshold_db):
                continue
                
            # Filter based on user preferences
            if show_nulls_only and avg_gain > -3:  # Only show nulls (negative gain)
                continue
            if show_peaks_only and avg_gain < 3:   # Only show peaks (positive gain)
                continue
                
            # Skip if variation is too small (unless smooth transitions)
            if not smooth_transitions and local_variation < 1.0:
                continue
            
            # Determine ring color and properties based on color scheme
            color, effect_type = self._get_ring_color_and_type(avg_gain, color_scheme)
            
            # Calculate opacity based on effect strength
            if smooth_transitions:
                ring_opacity = opacity * min(1.0, abs(avg_gain) / 15.0)  # Scale with gain
            else:
                ring_opacity = opacity * min(1.0, local_variation / 10.0)  # Scale with variation
            
            rings.append({
                'distance_km': distances_km[i],
                'center_lat': center_lat,
                'center_lon': center_lon,
                'gain_db': avg_gain,
                'variation_db': local_variation,
                'color': color,
                'effect_type': effect_type,
                'opacity': max(0.1, ring_opacity),  # Ensure minimum visibility
                'width': 3 if smooth_transitions else 2  # Wider for smooth transitions
            })
        
        return rings
    
    def _get_ring_color_and_type(self, gain_db, color_scheme):
        """
        Get ring color and type based on gain and color scheme.
        
        Args:
            gain_db (float): Gain in dB
            color_scheme (str): Color scheme name
            
        Returns:
            tuple: (color_hex, effect_type)
        """
        if color_scheme == 'Rainbow (dB-based)':
            # Rainbow mapping: Blue (-20dB) -> Cyan -> Green -> Yellow -> Red (+20dB)
            # Normalize gain to 0-1 range
            normalized = np.clip((gain_db + 20) / 40, 0, 1)
            
            if normalized <= 0.25:  # Blue to Cyan
                r = 0
                g = int(255 * normalized * 4)
                b = 255
            elif normalized <= 0.5:  # Cyan to Green
                r = 0
                g = 255
                b = int(255 * (1 - (normalized - 0.25) * 4))
            elif normalized <= 0.75:  # Green to Yellow
                r = int(255 * (normalized - 0.5) * 4)
                g = 255
                b = 0
            else:  # Yellow to Red
                r = 255
                g = int(255 * (1 - (normalized - 0.75) * 4))
                b = 0
            
            color = f'#{r:02x}{g:02x}{b:02x}'
            effect_type = 'enhancement' if gain_db > 0 else 'nulling'
            
        elif color_scheme == 'Red/Blue (Enhancement/Nulling)':
            if gain_db > 2:
                color = '#FF0000'  # Red for enhancement
                effect_type = 'enhancement'
            elif gain_db < -5:
                color = '#0000FF'  # Blue for nulling
                effect_type = 'nulling'
            else:
                color = '#FFFF00'  # Yellow for moderate effect
                effect_type = 'moderate'
                
        elif color_scheme == 'Monochrome':
            # Grayscale based on effect strength
            intensity = min(255, int(255 * abs(gain_db) / 20))
            color = f'#{intensity:02x}{intensity:02x}{intensity:02x}'
            effect_type = 'enhancement' if gain_db > 0 else 'nulling'
            
        else:  # Custom or fallback
            color = '#FF00FF'  # Magenta for custom
            effect_type = 'custom'
        
        return color, effect_type
    
    def calculate_multipath_polygons(self, center_lat, center_lon, max_range_km, 
                                   effect_threshold_db=-10, color_scheme='Rainbow (dB-based)',
                                   opacity=0.6, show_nulls_only=False, show_peaks_only=False,
                                   smooth_transitions=True, ring_width_km=0.05):
        """
        Calculate multipath ring polygons for map visualization.
        
        Args:
            center_lat (float): Radar center latitude
            center_lon (float): Radar center longitude
            max_range_km (float): Maximum range in km
            effect_threshold_db (float): Threshold for significant multipath effect
            color_scheme (str): Color scheme for rings
            opacity (float): Ring opacity (0-1)
            show_nulls_only (bool): Show only nulls
            show_peaks_only (bool): Show only peaks
            smooth_transitions (bool): Use smooth transitions
            ring_width_km (float): Width of each ring polygon in km
        
        Returns:
            list: List of polygon dictionaries with coordinates and properties
        """
        if self.last_gain_pattern is None:
            self.calculate_multipath_gain()
        
        distances_km = self.distances / 1000
        gain_db = self.last_gain_pattern
        
        polygons = []
        step_size = 10 if smooth_transitions else 50
        
        for i in range(0, len(gain_db) - 1, step_size):
            if distances_km[i] > max_range_km:
                break
            
            # Calculate local variation and gain
            window_size = min(step_size, len(gain_db) - i)
            local_gain = gain_db[i:i+window_size]
            local_variation = np.std(local_gain)
            avg_gain = np.mean(local_gain)
            
            # Apply threshold filtering
            if abs(avg_gain) < abs(effect_threshold_db):
                continue
                
            # Filter based on user preferences
            if show_nulls_only and avg_gain > -3:
                continue
            if show_peaks_only and avg_gain < 3:
                continue
                
            # Skip if variation is too small (unless smooth transitions)
            if not smooth_transitions and local_variation < 1.0:
                continue
            
            # Determine ring color and properties
            color, effect_type = self._get_ring_color_and_type(avg_gain, color_scheme)
            
            # Calculate opacity based on effect strength
            if smooth_transitions:
                ring_opacity = opacity * min(1.0, abs(avg_gain) / 15.0)
            else:
                ring_opacity = opacity * min(1.0, local_variation / 10.0)
            
            # Create ring polygon coordinates
            inner_radius_km = distances_km[i] - ring_width_km/2
            outer_radius_km = distances_km[i] + ring_width_km/2
            
            # Ensure inner radius is not negative
            inner_radius_km = max(0, inner_radius_km)
            
            # Generate polygon coordinates (outer ring - inner ring for donut shape)
            polygon_coords = self._generate_ring_polygon_coords(
                center_lat, center_lon, inner_radius_km, outer_radius_km
            )
            
            if polygon_coords:
                polygons.append({
                    'distance_km': distances_km[i],
                    'inner_radius_km': inner_radius_km,
                    'outer_radius_km': outer_radius_km,
                    'center_lat': center_lat,
                    'center_lon': center_lon,
                    'gain_db': avg_gain,
                    'variation_db': local_variation,
                    'color': color,
                    'effect_type': effect_type,
                    'opacity': max(0.1, ring_opacity),
                    'coordinates': polygon_coords
                })
        
        return polygons
    
    def _generate_ring_polygon_coords(self, center_lat, center_lon, inner_radius_km, outer_radius_km):
        """
        Generate polygon coordinates for a ring (donut shape).
        
        Args:
            center_lat (float): Center latitude
            center_lon (float): Center longitude
            inner_radius_km (float): Inner radius in km
            outer_radius_km (float): Outer radius in km
            
        Returns:
            list: List of coordinate tuples [(lat, lon), ...]
        """
        import math
        
        def point_at_distance(lat, lon, distance_km, bearing_degrees):
            """Calculate point at distance and bearing from center."""
            R = 6371.0  # Earth's radius in km
            
            lat_rad = math.radians(lat)
            lon_rad = math.radians(lon)
            bearing_rad = math.radians(bearing_degrees)
            
            new_lat_rad = math.asin(
                math.sin(lat_rad) * math.cos(distance_km / R) +
                math.cos(lat_rad) * math.sin(distance_km / R) * math.cos(bearing_rad)
            )
            
            new_lon_rad = lon_rad + math.atan2(
                math.sin(bearing_rad) * math.sin(distance_km / R) * math.cos(lat_rad),
                math.cos(distance_km / R) - math.sin(lat_rad) * math.sin(new_lat_rad)
            )
            
            return math.degrees(new_lat_rad), math.degrees(new_lon_rad)
        
        coords = []
        
        # Generate outer ring coordinates (clockwise)
        for angle in range(0, 360, 5):  # 5-degree steps
            lat, lon = point_at_distance(center_lat, center_lon, outer_radius_km, angle)
            coords.append((lat, lon))
        
        # Close outer ring
        if coords:
            coords.append(coords[0])
        
        # If inner radius > 0, add inner ring (counter-clockwise to create hole)
        if inner_radius_km > 0:
            inner_coords = []
            for angle in range(355, -1, -5):  # Counter-clockwise
                lat, lon = point_at_distance(center_lat, center_lon, inner_radius_km, angle)
                inner_coords.append((lat, lon))
            
            # Close inner ring
            if inner_coords:
                inner_coords.append(inner_coords[0])
                coords.extend(inner_coords)
        
        # Ensure the entire polygon is closed
        if coords and coords[0] != coords[-1]:
            coords.append(coords[0])
        
        return coords
    
    def get_formatted_summary(self):
        """
        Get a formatted text summary of multipath analysis.
        
        Returns:
            str: Formatted summary string
        """
        summary = self.get_multipath_summary()
        
        if not summary:
            return "No multipath analysis performed yet."
        
        params = summary['parameters']
        stats = summary['statistics']
        
        # Format first null and peak
        first_null = f"{summary['nulls_km'][0]:.2f} km" if summary['nulls_km'] else "None detected"
        first_peak = f"{summary['peaks_km'][0]:.2f} km" if summary['peaks_km'] else "None detected"
        
        text = f"""
=== Multipath Analysis Summary ===
Parameters:
  • Radar Height: {params['radar_height']:.1f} m
  • Target Height: {params['target_height']:.1f} m
  • Reflectivity: {params['reflectivity']:.1f}%
  • Frequency: {params['frequency_ghz']:.1f} GHz
  • Analysis Range: {params['max_distance_km']:.1f} km

Statistics:
  • Maximum Gain: {stats['max_gain_db']:.1f} dB
  • Minimum Gain: {stats['min_gain_db']:.1f} dB
  • Mean Gain: {stats['mean_gain_db']:.1f} dB
  • Dynamic Range: {stats['dynamic_range_db']:.1f} dB

Multipath Effects:
  • Nulls Found: {summary['null_count']} locations
  • Peaks Found: {summary['peak_count']} locations
  • First Null: {first_null}
  • First Peak: {first_peak}
"""
        
        return text.strip()


class MultipathWidget:
    """
    PyQt6 widget for displaying multipath analysis plots.
    """
    
    def __init__(self, parent=None):
        self.analyzer = MultipathAnalyzer()
        self.figure = Figure(figsize=(12, 4))
        
        # Handle case where Qt backend is not available (for testing)
        if FigureCanvas is not None:
            self.canvas = FigureCanvas(self.figure)
        else:
            self.canvas = None
            
        self.ax = self.figure.add_subplot(111)
        self.line, = self.ax.plot([], [], 'b-', linewidth=1.5)
        self.init_plot()
    
    def init_plot(self):
        """Initialize the plot appearance."""
        self.ax.set_title("FMCW Radar Multipath Interference Pattern")
        self.ax.set_xlabel("Distance (km)")
        self.ax.set_ylabel("Relative Power (dB)")
        self.ax.grid(True, alpha=0.3)
        self.figure.tight_layout()
    
    def update_plot(self, radar_height, target_height, reflectivity, frequency_ghz, max_range_km,
                   color_scheme='Rainbow (dB-based)', show_rings=True, gain_threshold=-10, 
                   show_nulls_only=False, show_peaks_only=False):
        """
        Update the multipath plot with new parameters.
        
        Args:
            radar_height (float): Radar height in meters
            target_height (float): Target height in meters
            reflectivity (float): Reflectivity percentage
            frequency_ghz (float): Frequency in GHz
            max_range_km (float): Maximum range in km
            color_scheme (str): Color scheme for rings
            show_rings (bool): Whether to show multipath rings
            gain_threshold (float): Gain threshold for rings
            show_nulls_only (bool): Show only nulls
            show_peaks_only (bool): Show only peaks
        """
        # Update analyzer parameters
        self.analyzer.set_frequency(frequency_ghz)
        self.analyzer.set_geometry(radar_height, target_height, reflectivity)
        self.analyzer.set_analysis_range(max_range_km)
        
        # Calculate multipath gain
        distances_km, gain_db = self.analyzer.calculate_multipath_gain()
        
        # Clear and redraw plot
        self.ax.clear()
        
        # Plot main gain curve using the existing line object
        self.line, = self.ax.plot(distances_km, gain_db, 'b-', linewidth=1.5, label='Multipath Gain')
        
        # Add horizontal reference lines
        self.ax.axhline(y=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
        self.ax.axhline(y=gain_threshold, color='r', linestyle='--', alpha=0.5, linewidth=1, 
                       label=f'Threshold ({gain_threshold} dB)')
        
        # Add multipath rings if enabled
        if show_rings:
            rings = self.analyzer.calculate_multipath_rings(
                center_lat=0, center_lon=0, max_range_km=max_range_km,
                effect_threshold_db=gain_threshold, color_scheme=color_scheme,
                show_nulls_only=show_nulls_only, show_peaks_only=show_peaks_only,
                smooth_transitions=True
            )
            
            # Draw vertical lines for each ring
            for ring in rings:
                distance = ring['distance_km']
                color = ring['color']
                gain = ring['gain_db']
                alpha = ring['opacity']
                
                # Draw vertical line at ring position
                self.ax.axvline(x=distance, color=color, alpha=alpha, linewidth=2,
                               linestyle='-' if gain > 0 else '--')
                
                # Add small text label for significant rings
                if abs(gain) > abs(gain_threshold) * 1.5:
                    self.ax.text(distance, gain, f'{gain:.1f}dB', 
                               rotation=90, fontsize=8, color=color, alpha=0.8,
                               ha='center', va='bottom' if gain > 0 else 'top')
        
        # Highlight nulls and peaks
        nulls = self.analyzer.find_multipath_nulls(threshold_db=gain_threshold)
        peaks = self.analyzer.find_multipath_peaks(threshold_db=abs(gain_threshold))
        
        # Plot nulls
        for null_dist in nulls:
            if null_dist <= max_range_km:
                null_idx = np.argmin(np.abs(distances_km - null_dist))
                self.ax.plot(null_dist, gain_db[null_idx], 'ro', markersize=6, 
                           label='Nulls' if null_dist == nulls[0] else "", alpha=0.7)
        
        # Plot peaks
        for peak_dist in peaks:
            if peak_dist <= max_range_km:
                peak_idx = np.argmin(np.abs(distances_km - peak_dist))
                self.ax.plot(peak_dist, gain_db[peak_idx], 'g^', markersize=6,
                           label='Peaks' if peak_dist == peaks[0] else "", alpha=0.7)
        
        # Set title and labels
        self.ax.set_title(f"Multipath Pattern (f={frequency_ghz:.1f} GHz, hr={radar_height:.1f}m, ht={target_height:.1f}m)")
        self.ax.set_xlabel("Distance (km)")
        self.ax.set_ylabel("Relative Power (dB)")
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xlim(0, max_range_km)
        
        # Update y-limits based on data
        valid_gain = gain_db[np.isfinite(gain_db)]
        if valid_gain.size > 0:
            y_min = np.min(valid_gain) - 5
            y_max = np.max(valid_gain) + 5
            self.ax.set_ylim(y_min, y_max)
        
        # Add distance grid lines
        for km in range(0, int(max_range_km) + 1, 5):
            self.ax.axvline(km, color='gray', alpha=0.2, linestyle='--')
        
        # Add legend if rings are shown
        if show_rings or nulls or peaks:
            self.ax.legend(loc='upper right', fontsize=8)
        
        # Refresh canvas (if available)
        if self.canvas is not None:
            self.canvas.draw()
    
    def get_canvas(self):
        """Get the matplotlib canvas widget."""
        return self.canvas
    
    def get_analyzer(self):
        """Get the multipath analyzer instance."""
        return self.analyzer