import sys
import os
import json
import time
from datetime import datetime
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QLabel, QSpinBox, QDoubleSpinBox, QCheckBox, 
                             QProgressBar, QTextEdit, QPushButton, QComboBox,
                             QGroupBox, QGridLayout, QSplitter, QFrame,
                             QSlider, QApplication, QMessageBox, QFileDialog,
                             QScrollArea, QLineEdit, QStatusBar, QMenuBar, QMenu)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, pyqtSlot, QTimer, QUrl
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWebChannel import QWebChannel
from PyQt6.QtWebEngineCore import QWebEngineSettings
from PyQt6.QtGui import QFont, QIcon

import folium
from folium import plugins
import numpy as np
import tempfile
import matplotlib.pyplot as plt
from skimage import measure
from PIL import Image, ImageDraw

# Import modules
from utils.distance_calculator import DistanceCalculator
from utils.multipath_analyzer import MultipathAnalyzer
from utils.multipath_analyzer import MultipathWidget
from utils.color_utils import ColorUtils
from utils.coordinate_utils import decimal_to_dms, format_dms_string, get_srtm_bounds
from interface.worker import Worker
from interface.settings_dialog import SettingsDialog
from interface.about_dialog import AboutDialog
from elevation.srtm_verification_dialog import SRTMVerificationDialog
from elevation.srtm_downloader import SRTMDownloader
from elevation import srtm_handler
from visibility_2d import Visibility2DAnalyzer

class JavaScriptIntegrationManager:
    """
    Manager class to handle JavaScript integration timing issues
    """
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.map_ready = False
        self.pending_js_calls = []
        self.check_attempts = 0
        self.max_check_attempts = 50  # 5 seconds max wait
        
    def wait_for_map_ready(self, callback, timeout=5000):
        """Wait for map to be ready before executing JavaScript"""
        if self.map_ready:
            callback()
            return
            
        # Store callback for later execution
        self.pending_js_calls.append(callback)
        
        # Check if map is ready with improved detection
        check_js = """
        (function() {
            try {
                // Check multiple ways to detect if map is ready
                var mapElement = document.querySelector('#map');
                var leafletAvailable = typeof L !== 'undefined';
                var clearRangeRingsAvailable = typeof clearRangeRings === 'function';
                var addMultipathRingsAvailable = typeof addMultipathRings === 'function';
                
                // Try to access the map instance
                var mapInstance = null;
                if (mapElement && mapElement._leaflet) {
                    mapInstance = mapElement._leaflet;
                } else if (window.map) {
                    mapInstance = window.map;
                }
                
                // More permissive readiness check - just need basic map functionality
                var basicReady = leafletAvailable && (!!mapInstance || !!mapElement);
                var fullReady = basicReady && clearRangeRingsAvailable && addMultipathRingsAvailable;
                
                return {
                    mapElement: !!mapElement,
                    leafletAvailable: leafletAvailable,
                    clearRangeRingsAvailable: clearRangeRingsAvailable,
                    addMultipathRingsAvailable: addMultipathRingsAvailable,
                    mapInstance: !!mapInstance,
                    basicReady: basicReady,
                    fullReady: fullReady,
                    ready: fullReady  // Prefer full readiness but could fallback to basic
                };
            } catch (e) {
                return {
                    mapElement: false,
                    leafletAvailable: false,
                    clearRangeRingsAvailable: false,
                    addMultipathRingsAvailable: false,
                    mapInstance: false,
                    basicReady: false,
                    fullReady: false,
                    ready: false,
                    error: e.toString()
                };
            }
        })();
        """
        
        def check_result(result):
            # Accept either full readiness or basic readiness after some attempts
            ready = False
            if result:
                if result.get('ready', False):
                    ready = True
                elif self.check_attempts > 20 and result.get('basicReady', False):
                    # After 2 seconds, accept basic readiness (map available but maybe not all functions)
                    ready = True
                    print("Using basic map readiness (not all JavaScript functions available)")
            
            if ready:
                self.map_ready = True
                self.check_attempts = 0
                # Execute all pending callbacks
                for pending_callback in self.pending_js_calls:
                    try:
                        pending_callback()
                    except Exception as e:
                        print(f"Error executing pending JavaScript callback: {e}")
                self.pending_js_calls.clear()
            else:
                self.check_attempts += 1
                if self.check_attempts < self.max_check_attempts:
                    # Try again in 100ms
                    QTimer.singleShot(100, lambda: self.wait_for_map_ready(callback, timeout - 100))
                else:
                    # Give up and clear pending calls (don't force execute)
                    print(f"Map readiness check timed out after {self.max_check_attempts} attempts - skipping JavaScript calls")
                    self.check_attempts = 0
                    self.pending_js_calls.clear()
        
        if hasattr(self.main_window, 'map_view') and self.main_window.map_view:
            self.main_window.map_view.page().runJavaScript(check_js, check_result)
        else:
            # If no map view, just execute callback
            callback()
    
    def safe_javascript_call(self, js_code, callback=None):
        """Safely execute JavaScript code only when map is ready"""
        def execute_js():
            try:
                if hasattr(self.main_window, 'map_view') and self.main_window.map_view:
                    # Handle callback properly - PyQt6 doesn't accept None callbacks
                    if callback is not None:
                        self.main_window.map_view.page().runJavaScript(js_code, callback)
                    else:
                        self.main_window.map_view.page().runJavaScript(js_code)
                else:
                    print(f"Map view not available, skipping JavaScript call")
            except Exception as e:
                print(f"Error executing JavaScript: {e}")
        
        self.wait_for_map_ready(execute_js)
    
    def reset_map_ready_state(self):
        """Reset the map ready state when map is reloaded"""
        self.map_ready = False
        self.check_attempts = 0

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Blighter Viewshed Analysis Tool")
        self.setMinimumSize(1650, 1050)  # Increased size to accommodate wider parameter panel
        
        # Config file path
        self.config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.json')
        
        # Initialize settings with default values
        self.settings = {
            'distance_rings': True,
            'use_google_elevation': False,
            'map_provider': 'OpenStreetMap',
            'srtm_username': '',
            'srtm_password': '',
            'last_parameters': {
                'latitude': 50.314852,
                'longitude': 23.302799,
                'height': 30,
                'target_height': 2,
                'detection_distance': 3,
                'hbeam_min': 0,
                'hbeam_max': 360,
                'vbeam_min': -20,
                'vbeam_max': 20,
                'color_pattern': "Red",
                'opacity': 0.70,
                'enable_rings': True,
                'ring_interval': 1,
                'frequency': 10.0,
                'reflectivity': 0.5,  # Keep for backwards compatibility
                'land_reflectivity': 0.3,
                'water_reflectivity': 0.9,
                'enable_multipath_rings': False,
                'multipath_color_scheme': 'Rainbow (dB-based)',
                'multipath_opacity': 0.6,
                'multipath_map_opacity': 0.4,
                'multipath_ring_style': 'Filled Polygons',
                'gain_threshold': -10,
                'show_nulls_only': False,
                'show_peaks_only': False,
                'use_parallel_processing': True
            }
        }
        
        # Load saved settings if they exist
        self.load_settings()
        
        # Initialize analysis components
        self.distance_calculator = DistanceCalculator()
        self.multipath_analyzer = MultipathAnalyzer()
        
        # Initialize JavaScript integration manager
        self.js_manager = JavaScriptIntegrationManager(self)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create the central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        layout.setSpacing(8)  # Reduced spacing between left panel and map
        
        # Create left panel for parameters with scroll area
        left_panel_container = QWidget()
        left_panel_container.setMaximumWidth(360)  # Increased to 320px for better text visibility
        left_panel_container.setMinimumWidth(360)
        
        # Create scroll area for parameter panel
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        left_panel = self.create_parameter_panel()
        scroll_area.setWidget(left_panel)
        
        # Set up left panel container layout
        left_container_layout = QVBoxLayout(left_panel_container)
        left_container_layout.setContentsMargins(0, 0, 0, 0)
        left_container_layout.addWidget(scroll_area)
        
        layout.addWidget(left_panel_container)
        
        # Create right panel for map and results
        right_panel = self.create_map_panel()
        layout.addWidget(right_panel, stretch=1)  # Make map panel stretch
        
        # Set up status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
        
        # Initialize threading components
        self.thread = None
        self.worker = None
        
        # Add a progress bar to the status bar
        self.progress_bar = QProgressBar()
        self.status_bar.addPermanentWidget(self.progress_bar)
        self.progress_bar.hide()
        
        # Initialize debounce timer for real-time analysis
        self.analysis_update_timer = QTimer()
        self.analysis_update_timer.setSingleShot(True)
        self.analysis_update_timer.setInterval(500)  # 500ms delay
        self.analysis_update_timer.timeout.connect(self.update_realtime_analysis)
        
        # Initialize the map
        self.initialize_map()
        self.current_zoom = 13  # Add default zoom level
        
        # Perform initial real-time analysis update to populate multipath pattern on startup
        # Use QTimer.singleShot to ensure this happens after UI is fully initialized
        QTimer.singleShot(100, self.update_realtime_analysis)
        
        # Automatically go to location on startup to load elevation data
        # Use a slightly longer delay to ensure map is ready before loading location
        QTimer.singleShot(300, self.goto_location)

    def create_menu_bar(self):
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        file_menu.addAction("Settings", self.show_settings)
        file_menu.addSeparator()
        file_menu.addAction("Export KML", self.export_kml)
        file_menu.addAction("Export CSV", self.export_csv)
        file_menu.addAction("Export GeoJSON", self.export_geojson)
        file_menu.addSeparator()
        file_menu.addAction("Exit", self.close)
        
        # Help menu
        help_menu = menubar.addMenu("Help")
        help_menu.addAction("About", self.show_about)

    def create_parameter_panel(self):
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(8)  # Reduced spacing between groups
        layout.setContentsMargins(5, 5, 5, 5)  # Reduced margins
        
        # Location Parameters Group
        location_group = QGroupBox("Location Parameters")
        location_layout = QVBoxLayout()
        
        # Add navigation buttons
        nav_layout = QHBoxLayout()
        
        self.goto_location_btn = QPushButton("Go to Location and load elevation data")
        self.goto_location_btn.clicked.connect(self.goto_location)
        nav_layout.addWidget(self.goto_location_btn)
        
        self.verify_srtm_btn = QPushButton("Verify SRTM Data")
        self.verify_srtm_btn.clicked.connect(self.verify_srtm_data)
        self.verify_srtm_btn.setEnabled(False)  # Disabled until SRTM data is loaded
        nav_layout.addWidget(self.verify_srtm_btn)
        
        location_layout.addLayout(nav_layout)

        # Latitude input
        lat_layout = QHBoxLayout()
        lat_label = QLabel("Latitude:")
        self.lat_input = QDoubleSpinBox()
        self.lat_input.setRange(-90, 90)
        self.lat_input.setDecimals(6)
        self.lat_input.setValue(self.settings['last_parameters']['latitude'])
        lat_layout.addWidget(lat_label)
        lat_layout.addWidget(self.lat_input)
        location_layout.addLayout(lat_layout)
        
        # Longitude input
        lon_layout = QHBoxLayout()
        lon_label = QLabel("Longitude:")
        self.lon_input = QDoubleSpinBox()
        self.lon_input.setRange(-180, 180)
        self.lon_input.setDecimals(6)
        self.lon_input.setValue(self.settings['last_parameters']['longitude'])
        lon_layout.addWidget(lon_label)
        lon_layout.addWidget(self.lon_input)
        location_layout.addLayout(lon_layout)
        
        location_group.setLayout(location_layout)
        layout.addWidget(location_group)
        
        # Radar Parameters Group
        radar_group = QGroupBox("Radar Parameters")
        radar_layout = QVBoxLayout()
        
        # Radar Height
        height_layout = QHBoxLayout()
        height_label = QLabel("Radar Height - AGL:")
        self.height_input = QSpinBox()
        self.height_input.setRange(0, 1000)
        self.height_input.setValue(self.settings['last_parameters']['height'])
        self.height_input.setSuffix("m")
        height_layout.addWidget(height_label)
        height_layout.addWidget(self.height_input)
        radar_layout.addLayout(height_layout)
        
        # Horizontal Beam Angles
        hbeam_layout = QHBoxLayout()
        hbeam_label = QLabel("Horizontal Beam Angle:")
        self.hbeam_min = QSpinBox()
        self.hbeam_max = QSpinBox()
        self.hbeam_min.setRange(0, 360)
        self.hbeam_max.setRange(0, 360)
        self.hbeam_min.setValue(self.settings['last_parameters']['hbeam_min'])
        self.hbeam_max.setValue(self.settings['last_parameters']['hbeam_max'])
        self.hbeam_min.setSuffix("°")
        self.hbeam_max.setSuffix("°")
        hbeam_layout.addWidget(hbeam_label)
        hbeam_layout.addWidget(self.hbeam_min)
        hbeam_layout.addWidget(QLabel("to"))
        hbeam_layout.addWidget(self.hbeam_max)
        radar_layout.addLayout(hbeam_layout)
        
        # Vertical Beam Angles
        vbeam_layout = QHBoxLayout()
        vbeam_label = QLabel("Vertical Beam Angle:")
        self.vbeam_min = QSpinBox()
        self.vbeam_max = QSpinBox()
        self.vbeam_min.setRange(-90, 90)
        self.vbeam_max.setRange(-90, 90)
        self.vbeam_min.setValue(self.settings['last_parameters']['vbeam_min'])
        self.vbeam_max.setValue(self.settings['last_parameters']['vbeam_max'])
        self.vbeam_min.setSuffix("°")
        self.vbeam_max.setSuffix("°")
        vbeam_layout.addWidget(vbeam_label)
        vbeam_layout.addWidget(self.vbeam_min)
        vbeam_layout.addWidget(QLabel("to"))
        vbeam_layout.addWidget(self.vbeam_max)
        radar_layout.addLayout(vbeam_layout)
        
        # Frequency
        freq_layout = QHBoxLayout()
        freq_label = QLabel("Frequency (GHz):")
        self.freq_input = QDoubleSpinBox()
        self.freq_input.setRange(0.1, 100)
        self.freq_input.setValue(self.settings['last_parameters']['frequency'])
        self.freq_input.setDecimals(1)
        self.freq_input.setSingleStep(0.1)
        freq_layout.addWidget(freq_label)
        freq_layout.addWidget(self.freq_input)
        radar_layout.addLayout(freq_layout)

        # Land Reflectivity
        land_refl_layout = QHBoxLayout()
        land_refl_label = QLabel("Land Reflectivity:")
        self.land_refl_input = QDoubleSpinBox()
        self.land_refl_input.setRange(0, 1)
        self.land_refl_input.setValue(self.settings['last_parameters']['land_reflectivity'])
        self.land_refl_input.setDecimals(2)
        self.land_refl_input.setSingleStep(0.05)
        land_refl_layout.addWidget(land_refl_label)
        land_refl_layout.addWidget(self.land_refl_input)
        radar_layout.addLayout(land_refl_layout)
        
        # Water Reflectivity
        water_refl_layout = QHBoxLayout()
        water_refl_label = QLabel("Water Reflectivity:")
        self.water_refl_input = QDoubleSpinBox()
        self.water_refl_input.setRange(0, 1)
        self.water_refl_input.setValue(self.settings['last_parameters']['water_reflectivity'])
        self.water_refl_input.setDecimals(2)
        self.water_refl_input.setSingleStep(0.05)
        water_refl_layout.addWidget(water_refl_label)
        water_refl_layout.addWidget(self.water_refl_input)
        radar_layout.addLayout(water_refl_layout)
        
        radar_group.setLayout(radar_layout)
        layout.addWidget(radar_group)
        
        # Target Parameters Group
        target_group = QGroupBox("Target Parameters")
        target_layout = QVBoxLayout()
        
        # Target Height
        target_height_layout = QHBoxLayout()
        target_height_label = QLabel("Target Height - Rel. to Gnd.:")
        self.target_height = QSpinBox()
        self.target_height.setRange(0, 1000)
        self.target_height.setValue(self.settings['last_parameters']['target_height'])
        self.target_height.setSuffix("m")
        target_height_layout.addWidget(target_height_label)
        target_height_layout.addWidget(self.target_height)
        target_layout.addLayout(target_height_layout)
        
        # Detection Distance
        detection_layout = QHBoxLayout()
        detection_label = QLabel("Detection Distance:")
        self.detection_distance = QSpinBox()
        self.detection_distance.setRange(0, 100)
        self.detection_distance.setValue(self.settings['last_parameters']['detection_distance'])
        self.detection_distance.setSuffix("km")
        detection_layout.addWidget(detection_label)
        detection_layout.addWidget(self.detection_distance)
        target_layout.addLayout(detection_layout)
        
        target_group.setLayout(target_layout)
        layout.addWidget(target_group)
        
        # Visualization Group
        viz_group = QGroupBox("Visualization")
        viz_layout = QVBoxLayout()
        
        # Color pattern selection
        color_layout = QHBoxLayout()
        color_label = QLabel("Color Pattern:")
        self.color_pattern = QComboBox()
        
        # Get available color patterns from ColorUtils
        available_patterns = ColorUtils.get_available_patterns()
        pattern_descriptions = ColorUtils.get_pattern_descriptions()
        
        # Add patterns with descriptions as tooltips
        for pattern in available_patterns:
            self.color_pattern.addItem(pattern)
            description = pattern_descriptions.get(pattern, pattern)
            self.color_pattern.setItemData(
                self.color_pattern.count() - 1, 
                description, 
                Qt.ItemDataRole.ToolTipRole
            )
        
        # Set current value, fallback to first available if not found
        current_pattern = self.settings['last_parameters']['color_pattern']
        if current_pattern in available_patterns:
            self.color_pattern.setCurrentText(current_pattern)
        else:
            self.color_pattern.setCurrentText(available_patterns[0])
            
        self.color_pattern.currentTextChanged.connect(self.on_color_pattern_changed)
        color_layout.addWidget(color_label)
        color_layout.addWidget(self.color_pattern)
        viz_layout.addLayout(color_layout)
        
        # Overlay Opacity
        opacity_layout = QHBoxLayout()
        opacity_label = QLabel("Overlay Opacity:")
        self.opacity = QDoubleSpinBox()
        self.opacity.setRange(0, 1)
        self.opacity.setValue(self.settings['last_parameters']['opacity'])
        self.opacity.setSingleStep(0.1)
        opacity_layout.addWidget(opacity_label)
        opacity_layout.addWidget(self.opacity)
        viz_layout.addLayout(opacity_layout)
        
        viz_group.setLayout(viz_layout)
        layout.addWidget(viz_group)
        
        # Range Rings Group
        range_group = QGroupBox("Range Rings")
        range_layout = QVBoxLayout()
        
        # Enable Range Rings
        self.enable_rings = QCheckBox("Enable Range Rings")
        self.enable_rings.setChecked(self.settings['last_parameters']['enable_rings'])
        range_layout.addWidget(self.enable_rings)
        
        # Ring Interval
        ring_layout = QHBoxLayout()
        ring_label = QLabel("Ring Interval:")
        self.ring_interval = QSpinBox()
        self.ring_interval.setRange(1, 10)
        self.ring_interval.setValue(self.settings['last_parameters']['ring_interval'])
        self.ring_interval.setSuffix("km")
        ring_layout.addWidget(ring_label)
        ring_layout.addWidget(self.ring_interval)
        range_layout.addLayout(ring_layout)
        
        range_group.setLayout(range_layout)
        layout.addWidget(range_group)
        
        # Multipath Rings Group
        multipath_group = QGroupBox("Multipath Rings")
        multipath_layout = QVBoxLayout()
        
        # Enable Multipath Rings
        self.enable_multipath_rings = QCheckBox("Enable Multipath Rings")
        self.enable_multipath_rings.setChecked(self.settings['last_parameters'].get('enable_multipath_rings', False))
        multipath_layout.addWidget(self.enable_multipath_rings)
        
        # Color Scheme Selection
        color_scheme_layout = QHBoxLayout()
        color_scheme_label = QLabel("Color Scheme:")
        self.multipath_color_scheme = QComboBox()
        self.multipath_color_scheme.addItems([
            'Rainbow (dB-based)',
            'Red/Blue (Enhancement/Nulling)',
            'Monochrome',
            'Custom'
        ])
        self.multipath_color_scheme.setCurrentText(self.settings['last_parameters'].get('multipath_color_scheme', 'Rainbow (dB-based)'))
        color_scheme_layout.addWidget(color_scheme_label)
        color_scheme_layout.addWidget(self.multipath_color_scheme)
        multipath_layout.addLayout(color_scheme_layout)
        
        # Multipath Opacity
        multipath_opacity_layout = QHBoxLayout()
        multipath_opacity_label = QLabel("Ring Opacity:")
        self.multipath_opacity = QDoubleSpinBox()
        self.multipath_opacity.setRange(0.1, 1.0)
        self.multipath_opacity.setValue(self.settings['last_parameters'].get('multipath_opacity', 0.6))
        self.multipath_opacity.setSingleStep(0.1)
        multipath_opacity_layout.addWidget(multipath_opacity_label)
        multipath_opacity_layout.addWidget(self.multipath_opacity)
        multipath_layout.addLayout(multipath_opacity_layout)
        
        # Gain Threshold
        gain_threshold_layout = QHBoxLayout()
        gain_threshold_label = QLabel("Gain Threshold:")
        self.gain_threshold = QDoubleSpinBox()
        self.gain_threshold.setRange(-30, 10)
        self.gain_threshold.setValue(self.settings['last_parameters'].get('gain_threshold', -10))
        self.gain_threshold.setSuffix(" dB")
        gain_threshold_layout.addWidget(gain_threshold_label)
        gain_threshold_layout.addWidget(self.gain_threshold)
        multipath_layout.addLayout(gain_threshold_layout)
        
        # Map Display Options
        map_display_layout = QVBoxLayout()
        map_display_label = QLabel("Map Display:")
        map_display_layout.addWidget(map_display_label)
        
        # Map Fill Opacity
        map_opacity_layout = QHBoxLayout()
        map_opacity_label = QLabel("Map Fill Opacity:")
        self.multipath_map_opacity = QDoubleSpinBox()
        self.multipath_map_opacity.setRange(0.0, 1.0)
        self.multipath_map_opacity.setValue(self.settings['last_parameters'].get('multipath_map_opacity', 0.4))
        self.multipath_map_opacity.setSingleStep(0.1)
        map_opacity_layout.addWidget(map_opacity_label)
        map_opacity_layout.addWidget(self.multipath_map_opacity)
        map_display_layout.addLayout(map_opacity_layout)
        
        # Ring Style
        ring_style_layout = QHBoxLayout()
        ring_style_label = QLabel("Ring Style:")
        self.multipath_ring_style = QComboBox()
        self.multipath_ring_style.addItems([
            'Filled Polygons',
            'Outlines Only',
            'Both'
        ])
        self.multipath_ring_style.setCurrentText(self.settings['last_parameters'].get('multipath_ring_style', 'Filled Polygons'))
        ring_style_layout.addWidget(ring_style_label)
        ring_style_layout.addWidget(self.multipath_ring_style)
        map_display_layout.addLayout(ring_style_layout)
        
        multipath_layout.addLayout(map_display_layout)
        
        # Show Options
        show_options_layout = QHBoxLayout()
        self.show_nulls_only = QCheckBox("Nulls Only")
        self.show_peaks_only = QCheckBox("Peaks Only")
        self.show_nulls_only.setChecked(self.settings['last_parameters'].get('show_nulls_only', False))
        self.show_peaks_only.setChecked(self.settings['last_parameters'].get('show_peaks_only', False))
        show_options_layout.addWidget(self.show_nulls_only)
        show_options_layout.addWidget(self.show_peaks_only)
        multipath_layout.addLayout(show_options_layout)
        
        multipath_group.setLayout(multipath_layout)
        layout.addWidget(multipath_group)
        
        # Performance Options Group
        performance_group = QGroupBox("Performance Options")
        performance_layout = QVBoxLayout()
        
        # Parallel Processing
        self.enable_parallel_processing = QCheckBox("Enable Parallel Processing")
        self.enable_parallel_processing.setChecked(self.settings['last_parameters'].get('use_parallel_processing', True))
        self.enable_parallel_processing.setToolTip("Use multiple CPU cores for faster viewshed calculation (enabled by default)")
        performance_layout.addWidget(self.enable_parallel_processing)
        
        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)
        
        # Distance Analysis Group
        distance_group = QGroupBox("Distance Analysis")
        distance_layout = QVBoxLayout()
        
        # Distance calculation display
        self.distance_display = QTextEdit()
        self.distance_display.setMaximumHeight(100)  # Reduced from 120px to 100px
        self.distance_display.setMinimumHeight(80)   # Added minimum height for flexibility
        self.distance_display.setReadOnly(True)
        self.distance_display.setStyleSheet("QTextEdit { font-family: monospace; font-size: 9pt; }")
        distance_layout.addWidget(self.distance_display)
        
        distance_group.setLayout(distance_layout)
        layout.addWidget(distance_group)
        
        # Configuration Buttons
        config_layout = QHBoxLayout()
        self.save_config_btn = QPushButton("Save Config to Json")
        self.load_config_btn = QPushButton("Load Config from Json")
        config_layout.addWidget(self.save_config_btn)
        config_layout.addWidget(self.load_config_btn)
        layout.addLayout(config_layout)
        
        # Connect parameter changes to real-time updates
        self.height_input.valueChanged.connect(self.trigger_analysis_update)
        self.target_height.valueChanged.connect(self.trigger_analysis_update)
        self.detection_distance.valueChanged.connect(self.trigger_analysis_update)
        self.freq_input.valueChanged.connect(self.trigger_analysis_update)
        self.land_refl_input.valueChanged.connect(self.trigger_analysis_update)
        self.water_refl_input.valueChanged.connect(self.trigger_analysis_update)
        self.hbeam_min.valueChanged.connect(self.trigger_analysis_update)
        self.hbeam_max.valueChanged.connect(self.trigger_analysis_update)
        self.vbeam_min.valueChanged.connect(self.trigger_analysis_update)
        self.vbeam_max.valueChanged.connect(self.trigger_analysis_update)
        
        # Connect multipath controls to real-time updates
        self.enable_multipath_rings.stateChanged.connect(self.trigger_analysis_update)
        self.multipath_color_scheme.currentTextChanged.connect(self.trigger_analysis_update)
        self.multipath_opacity.valueChanged.connect(self.trigger_analysis_update)
        self.multipath_map_opacity.valueChanged.connect(self.trigger_analysis_update)
        self.multipath_ring_style.currentTextChanged.connect(self.trigger_analysis_update)
        self.gain_threshold.valueChanged.connect(self.trigger_analysis_update)
        self.show_nulls_only.stateChanged.connect(self.trigger_analysis_update)
        self.show_peaks_only.stateChanged.connect(self.trigger_analysis_update)
        
        # Initial calculation
        self.update_realtime_analysis()
        
        # Add stretch to push everything up
        layout.addStretch()
        
        return panel

    def create_map_panel(self):
        """Create the right panel containing map and controls"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(5)  # Reduce spacing between elements
        
        # Create vertical splitter for map and multipath panel
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Create map view with proper settings
        self.map_view = QWebEngineView()
        settings = self.map_view.page().settings()
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessFileUrls, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
        
        # Set up web channel for JavaScript communication
        self.channel = QWebChannel(self.map_view.page())
        self.map_view.page().setWebChannel(self.channel)
        self.channel.registerObject('handler', self)
        
        # Add map to splitter
        splitter.addWidget(self.map_view)
        
        # Create multipath panel
        multipath_panel = QWidget()
        multipath_panel.setMinimumHeight(250)  # Ensure minimum height for multipath visualization
        multipath_layout = QVBoxLayout(multipath_panel)
        multipath_layout.setSpacing(5)
        
        # Create multipath widget
        self.multipath_widget = MultipathWidget()
        canvas = self.multipath_widget.get_canvas()
        canvas.setMinimumHeight(220)  # Ensure minimum height for the plot
        multipath_layout.addWidget(canvas)
        
        # Add multipath panel to splitter
        splitter.addWidget(multipath_panel)
        
        # Set splitter proportions (70% map, 30% multipath for better visibility)
        splitter.setSizes([700, 300])
        
        # Add splitter to layout
        layout.addWidget(splitter, stretch=1)
        
        # Create button panel
        button_layout = QHBoxLayout()
        button_layout.setSpacing(5)  # Set spacing between buttons
        
        # First row of buttons
        self.run_simulation_btn = QPushButton(" Run Simulation")
        self.export_kml_btn = QPushButton(" Export to KML")
        self.extract_srtm_btn = QPushButton(" Extract SRTM to Map")
        self.export_srtm_kml_btn = QPushButton(" Export SRTM to KML")
        
        button_layout.addWidget(self.run_simulation_btn)
        button_layout.addWidget(self.export_kml_btn)
        button_layout.addWidget(self.extract_srtm_btn)
        button_layout.addWidget(self.export_srtm_kml_btn)
        
        # Add button handlers
        self.run_simulation_btn.clicked.connect(self.run_simulation_threaded)
        self.export_kml_btn.clicked.connect(self.export_kml)
        self.extract_srtm_btn.clicked.connect(self.extract_srtm)
        self.export_srtm_kml_btn.clicked.connect(self.export_srtm_kml)
        
        # Add button layout to main layout
        button_container = QWidget()
        button_container.setLayout(button_layout)
        layout.addWidget(button_container)
        
        # Create results text area
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(120)  # Reduced from 150px to 120px
        self.results_text.setMinimumHeight(80)   # Added minimum height for flexibility
        self.results_text.setReadOnly(True)
        layout.addWidget(self.results_text)
        
        return panel

    def initialize_map(self):
        """Initialize the map with coordinate display and right-click menu"""
        # Create a folium map centered on default coordinates
        m = folium.Map(
            location=[self.lat_input.value(), self.lon_input.value()],
            zoom_start=13,
            prefer_canvas=True
        )

        # Add mouse position control to show coordinates
        lat_formatter = """
            function(num) {
                function getHGTFilename(lat, lon) {
                    let latDeg = Math.floor(Math.abs(lat));
                    let lonDeg = Math.floor(Math.abs(lon));
                    let ns = lat >= 0 ? 'N' : 'S';
                    let ew = lon >= 0 ? 'E' : 'W';
                    return `${ns}${latDeg.toString().padStart(2, '0')}${ew}${lonDeg.toString().padStart(3, '0')}.hgt`;
                }
                return L.Util.formatNum(num, 6) + '° | HGT: ' + getHGTFilename(num, window._lastLng || 0);
            }
        """
        lng_formatter = "function(num) { window._lastLng = num; return L.Util.formatNum(num, 6) + '°';}"
        
        plugins.MousePosition(
            position='topright',
            separator=' | ',
            empty_string='NaN',
            lng_first=False,
            num_digits=6,
            prefix='<div style="font-size: 14px; font-weight: bold; margin-bottom: 3px;">Coordinates:</div>',
            lat_formatter=lat_formatter,
            lng_formatter=lng_formatter,
        ).add_to(m)

        # Add custom right-click menu and dynamic map update functions
        folium.Element("""
            <script>
            function getHGTFilename(lat, lon) {
                let latDeg = Math.floor(Math.abs(lat));
                let lonDeg = Math.floor(Math.abs(lon));
                let ns = lat >= 0 ? 'N' : 'S';
                let ew = lon >= 0 ? 'E' : 'W';
                return `${ns}${latDeg.toString().padStart(2, '0')}${ew}${lonDeg.toString().padStart(3, '0')}.hgt`;
            }
            
            var currentMarker = null;
            var currentPopup = null;
            var visibilityLayer = null;
            var multipathLayerGroup = L.layerGroup();
            var radarMarker = null;
            var rangeRings = [];

            // Dynamic map update functions
            function clearLayers() {
                if (visibilityLayer && document.querySelector('#map')._leaflet.hasLayer(visibilityLayer)) {
                    document.querySelector('#map')._leaflet.removeLayer(visibilityLayer);
                }
                if (multipathLayerGroup) {
                    multipathLayerGroup.clearLayers();
                }
                if (radarMarker && document.querySelector('#map')._leaflet.hasLayer(radarMarker)) {
                    document.querySelector('#map')._leaflet.removeLayer(radarMarker);
                }
                // Clear range rings
                rangeRings.forEach(function(ring) {
                    if (document.querySelector('#map')._leaflet.hasLayer(ring)) {
                        document.querySelector('#map')._leaflet.removeLayer(ring);
                    }
                });
                rangeRings = [];
            }

            function addGeoJsonOverlay(geojson) {
                var map = document.querySelector('#map')._leaflet;
                visibilityLayer = L.geoJSON(geojson, {
                    style: function (feature) {
                        return {
                            fillColor: feature.properties.fill,
                            fillOpacity: feature.properties['fill-opacity'],
                            color: feature.properties.stroke,
                            weight: feature.properties['stroke-width'],
                            opacity: feature.properties['stroke-opacity']
                        };
                    }
                }).addTo(map);
            }

            function addRadarMarker(lat, lon, iconPath) {
                var map = document.querySelector('#map')._leaflet;
                radarMarker = L.marker([lat, lon], {
                    icon: L.icon({
                        iconUrl: iconPath,
                        iconSize: [35, 35],
                        iconAnchor: [17, 17]
                    })
                }).bindPopup("Radar Position").addTo(map);
            }

            function addRangeRings(centerLat, centerLon, maxRange, interval, color) {
                var map = document.querySelector('#map')._leaflet;
                for (var radius = interval; radius <= maxRange; radius += interval) {
                    var ring = L.circle([centerLat, centerLon], {
                        radius: radius,
                        color: color,
                        fill: false,
                        weight: 1
                    }).addTo(map);
                    rangeRings.push(ring);
                }
            }

            function addMultipathRings(rings) {
                var map = document.querySelector('#map')._leaflet;
                rings.forEach(function(ring) {
                    if (ring.type === 'polygon') {
                        L.polygon(ring.coordinates, {
                            color: ring.color,
                            weight: ring.weight || 1,
                            fill: ring.fill || true,
                            fillColor: ring.fillColor || ring.color,
                            fillOpacity: ring.fillOpacity || 0.5,
                            opacity: ring.opacity || 1.0
                        }).bindPopup(ring.popup || '').addTo(multipathLayerGroup);
                    } else if (ring.type === 'circle') {
                        L.circle([ring.center[0], ring.center[1]], {
                            radius: ring.radius,
                            color: ring.color,
                            weight: ring.weight || 2,
                            fill: ring.fill || false,
                            fillColor: ring.fillColor || ring.color,
                            fillOpacity: ring.fillOpacity || 0.5,
                            opacity: ring.opacity || 1.0
                        }).bindPopup(ring.popup || '').addTo(multipathLayerGroup);
                    }
                });
                multipathLayerGroup.addTo(map);
            }

            function updateMapCenter(lat, lon, zoom) {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    map.setView([lat, lon], zoom || map.getZoom());
                } catch (e) {
                    console.log('Error in updateMapCenter:', e);
                }
            }

            function clearRangeRings() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    rangeRings.forEach(function(ring) {
                        if (map.hasLayer(ring)) {
                            map.removeLayer(ring);
                        }
                    });
                    rangeRings = [];
                } catch (e) {
                    console.log('Error in clearRangeRings:', e);
                }
            }

            function clearMultipathRings() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    if (multipathLayerGroup) {
                        multipathLayerGroup.clearLayers();
                    }
                } catch (e) {
                    console.log('Error in clearMultipathRings:', e);
                }
            }

            function clearVisibilityLayer() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    if (visibilityLayer && map.hasLayer(visibilityLayer)) {
                        map.removeLayer(visibilityLayer);
                        visibilityLayer = null;
                    }
                } catch (e) {
                    console.log('Error in clearVisibilityLayer:', e);
                }
            }

            function addColorLegend(patternName, position) {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    
                    // Remove existing legend if any
                    var existingLegend = document.getElementById('color-legend');
                    if (existingLegend) {
                        existingLegend.remove();
                    }
                    
                    // Create legend container
                    var legend = L.control({position: position || 'bottomright'});
                    
                    legend.onAdd = function(map) {
                        var div = L.DomUtil.create('div', 'info legend');
                        div.id = 'color-legend';
                        div.style.backgroundColor = 'white';
                        div.style.padding = '10px';
                        div.style.border = '2px solid rgba(0,0,0,0.2)';
                        div.style.borderRadius = '5px';
                        div.style.fontSize = '12px';
                        div.style.minWidth = '150px';
                        
                        // Add title
                        div.innerHTML = '<h4 style="margin: 0 0 10px 0; text-align: center;">' + patternName + '</h4>';
                        
                        // Add gradient bar
                        var gradientDiv = document.createElement('div');
                        gradientDiv.style.height = '20px';
                        gradientDiv.style.marginBottom = '5px';
                        gradientDiv.style.borderRadius = '3px';
                        gradientDiv.style.border = '1px solid #ccc';
                        
                        // Create gradient based on pattern
                        var gradient = '';
                        if (patternName.includes('Red-Blue') || patternName.includes('Gradient')) {
                            gradient = 'linear-gradient(to right, #0000ff, #00ffff, #00ff00, #ffff00, #ff0000)';
                        } else if (patternName.includes('Rainbow')) {
                            gradient = 'linear-gradient(to right, #ff0000, #ff8000, #ffff00, #80ff00, #00ff00, #00ff80, #00ffff, #0080ff, #0000ff)';
                        } else if (patternName.includes('Terrain')) {
                            gradient = 'linear-gradient(to right, #8B4513, #A0522D, #CD853F, #DEB887, #F5DEB3, #90EE90, #228B22, #006400)';
                        } else if (patternName.includes('Heat')) {
                            gradient = 'linear-gradient(to right, #000000, #800000, #ff0000, #ff8000, #ffff00, #ffffff)';
                        } else if (patternName.includes('Viridis')) {
                            gradient = 'linear-gradient(to right, #440154, #31688E, #35B779, #FDE725)';
                        } else if (patternName.includes('Plasma')) {
                            gradient = 'linear-gradient(to right, #0D0887, #7E03A8, #CC4778, #F89441, #F0F921)';
                        } else {
                            // Solid color
                            var solidColor = '#ff0000'; // Default
                            if (patternName.includes('Green')) solidColor = '#00ff00';
                            else if (patternName.includes('Blue')) solidColor = '#0000ff';
                            else if (patternName.includes('Yellow')) solidColor = '#ffff00';
                            else if (patternName.includes('Purple')) solidColor = '#800080';
                            else if (patternName.includes('Cyan')) solidColor = '#00ffff';
                            gradient = solidColor;
                        }
                        
                        gradientDiv.style.background = gradient;
                        div.appendChild(gradientDiv);
                        
                        // Add labels
                        var labelsDiv = document.createElement('div');
                        labelsDiv.style.display = 'flex';
                        labelsDiv.style.justifyContent = 'space-between';
                        labelsDiv.style.fontSize = '10px';
                        
                        if (patternName.includes('Gradient') || patternName.includes('Rainbow') || 
                            patternName.includes('Terrain') || patternName.includes('Heat') || 
                            patternName.includes('Viridis') || patternName.includes('Plasma')) {
                            labelsDiv.innerHTML = '<span>Low</span><span>High</span>';
                        } else {
                            labelsDiv.innerHTML = '<span>Visibility</span>';
                        }
                        
                        div.appendChild(labelsDiv);
                        
                        return div;
                    };
                    
                    legend.addTo(map);
                    
                } catch (e) {
                    console.log('Error in addColorLegend:', e);
                }
            }

            function removeColorLegend() {
                try {
                    var existingLegend = document.getElementById('color-legend');
                    if (existingLegend) {
                        existingLegend.remove();
                    }
                } catch (e) {
                    console.log('Error in removeColorLegend:', e);
                }
            }

            document.addEventListener('DOMContentLoaded', function() {
                function waitForMap() {
                    var map = document.querySelector('#map');
                    if (map && map._leaflet) {
                        // Add right-click handler
                        map._leaflet.on('contextmenu', function(e) {
                            // Remove previous marker if it exists
                            if (currentMarker) {
                                map._leaflet.removeLayer(currentMarker);
                            }
                            
                            // Create new marker
                            currentMarker = L.marker(e.latlng).addTo(map._leaflet);
                            
                            // Create and open popup
                            var popup = L.popup({
                                closeButton: true,
                                closeOnClick: false,
                                autoClose: false
                            })
                            .setLatLng(e.latlng)
                            .setContent(`
                                <div style="text-align: center;">
                                    <div style="margin-bottom: 8px;">
                                        <strong style="font-size: 14px;">Selected Location</strong><br>
                                        <span style="font-family: monospace;">
                                            Lat: ${e.latlng.lat.toFixed(6)}° | Lon: ${e.latlng.lng.toFixed(6)}°<br>
                                            HGT File: ${getHGTFilename(e.latlng.lat, e.latlng.lng)}
                                        </span>
                                    </div>
                                    <button onclick="goToLocation(${e.latlng.lat}, ${e.latlng.lng})"
                                            style="padding: 8px 12px; cursor: pointer; background-color: #4CAF50; 
                                                   color: white; border: none; border-radius: 4px;
                                                   font-size: 13px; font-weight: bold;">
                                        Go to this location
                                    </button>
                                </div>
                            `)
                            .openOn(map._leaflet);
                            
                            // Store current popup
                            if (currentPopup) {
                                map._leaflet.closePopup(currentPopup);
                            }
                            currentPopup = popup;
                        });

                        // Clear marker when popup is closed
                        map._leaflet.on('popupclose', function(e) {
                            if (currentMarker) {
                                map._leaflet.removeLayer(currentMarker);
                                currentMarker = null;
                            }
                        });

                        // Setup map interaction event listeners
                        setupMapEventListeners();
                    } else {
                        setTimeout(waitForMap, 100);
                    }
                }
                waitForMap();
            });

            // Map interaction event handlers
            function setupMapEventListeners() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    if (map) {
                        // Listen for zoom end events
                        map.on('zoomend', function() {
                            var zoom = map.getZoom();
                            var center = map.getCenter();
                            new QWebChannel(qt.webChannelTransport, function(channel) {
                                channel.objects.handler.handleMapZoom(zoom, center.lat, center.lng);
                            });
                        });

                        // Listen for move end events (pan)
                        map.on('moveend', function() {
                            var center = map.getCenter();
                            new QWebChannel(qt.webChannelTransport, function(channel) {
                                channel.objects.handler.handleMapPan(center.lat, center.lng);
                            });
                        });
                    }
                } catch (e) {
                    console.log('Error setting up map event listeners:', e);
                }
            }
            });

            function getHGTFilename(lat, lon) {
                let latDeg = Math.floor(Math.abs(lat));
                let lonDeg = Math.floor(Math.abs(lon));
                let ns = lat >= 0 ? 'N' : 'S';
                let ew = lon >= 0 ? 'E' : 'W';
                return `${ns}${latDeg.toString().padStart(2, '0')}${ew}${lonDeg.toString().padStart(3, '0')}.hgt`;
            }
            
            function goToLocation(lat, lng) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    channel.objects.handler.handleMapClick(lat.toString(), lng.toString());
                });
            }

            // Map interaction event handlers
            function setupMapEventListeners() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    if (map) {
                        // Listen for zoom end events
                        map.on('zoomend', function() {
                            var zoom = map.getZoom();
                            var center = map.getCenter();
                            new QWebChannel(qt.webChannelTransport, function(channel) {
                                channel.objects.handler.handleMapZoom(zoom, center.lat, center.lng);
                            });
                        });

                        // Listen for move end events (pan)
                        map.on('moveend', function() {
                            var center = map.getCenter();
                            new QWebChannel(qt.webChannelTransport, function(channel) {
                                channel.objects.handler.handleMapPan(center.lat, center.lng);
                            });
                        });
                    }
                } catch (e) {
                    console.log('Error setting up map event listeners:', e);
                }
            }
            </script>
        """).add_to(m)

        # Save map to temporary HTML file
        temp_file = 'temp_map.html'
        m.save(temp_file)
        
        # Add QWebChannel script
        with open(temp_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        webchannel_script = '<script src="qrc:///qtwebchannel/qwebchannel.js"></script>'
        content = content.replace('</head>', f'{webchannel_script}</head>')
        
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)

        # Load the HTML into the QWebEngineView
        self.map_view.setUrl(QUrl.fromLocalFile(os.path.abspath(temp_file)))
        
        # Reset JavaScript integration manager state
        if hasattr(self, 'js_manager'):
            self.js_manager.reset_map_ready_state()

    def goto_location(self):
        """Navigate map to the current lat/lon coordinates"""
        try:
            lat = self.lat_input.value()
            lon = self.lon_input.value()
            
            # Create a folium map centered on the coordinates
            m = folium.Map(
                location=[lat, lon],
                zoom_start=13,
                prefer_canvas=True
            )

            # Add mouse position control to show coordinates
            lat_formatter = """
                function(num) {
                    function getHGTFilename(lat, lon) {
                        let latDeg = Math.floor(Math.abs(lat));
                        let lonDeg = Math.floor(Math.abs(lon));
                        let ns = lat >= 0 ? 'N' : 'S';
                        let ew = lon >= 0 ? 'E' : 'W';
                        return `${ns}${latDeg.toString().padStart(2, '0')}${ew}${lonDeg.toString().padStart(3, '0')}.hgt`;
                    }
                    return L.Util.formatNum(num, 6) + '° | HGT: ' + getHGTFilename(num, window._lastLng || 0);
                }
            """
            lng_formatter = "function(num) { window._lastLng = num; return L.Util.formatNum(num, 6) + '°';}"
            
            plugins.MousePosition(
                position='topright',
                separator=' | ',
                empty_string='NaN',
                lng_first=False,
                num_digits=6,
                prefix='<div style="font-size: 14px; font-weight: bold; margin-bottom: 3px;">Coordinates:</div>',
                lat_formatter=lat_formatter,
                lng_formatter=lng_formatter,
            ).add_to(m)

            # Add custom right-click menu and dynamic map update functions
            folium.Element("""
                <script>
                function getHGTFilename(lat, lon) {
                    let latDeg = Math.floor(Math.abs(lat));
                    let lonDeg = Math.floor(Math.abs(lon));
                    let ns = lat >= 0 ? 'N' : 'S';
                    let ew = lon >= 0 ? 'E' : 'W';
                    return `${ns}${latDeg.toString().padStart(2, '0')}${ew}${lonDeg.toString().padStart(3, '0')}.hgt`;
                }
                
                var currentMarker = null;
                var currentPopup = null;
                var visibilityLayer = null;
                var multipathLayerGroup = L.layerGroup();
                var radarMarker = null;
                var rangeRings = [];

                // Dynamic map update functions
                            function clearLayers() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    if (visibilityLayer && map.hasLayer(visibilityLayer)) {
                        map.removeLayer(visibilityLayer);
                    }
                    if (multipathLayerGroup) {
                        multipathLayerGroup.clearLayers();
                    }
                    if (radarMarker && map.hasLayer(radarMarker)) {
                        map.removeLayer(radarMarker);
                    }
                    // Clear range rings
                    rangeRings.forEach(function(ring) {
                        if (map.hasLayer(ring)) {
                            map.removeLayer(ring);
                        }
                    });
                    rangeRings = [];
                    // Clear SRTM overlay
                    if (window.srtmOverlay && map.hasLayer(window.srtmOverlay)) {
                        map.removeLayer(window.srtmOverlay);
                        window.srtmOverlay = null;
                    }
                } catch (e) {
                    console.log('Error in clearLayers:', e);
                }
            }

                function addGeoJsonOverlay(geojson) {
                    try {
                        var map = document.querySelector('#map')._leaflet;
                        visibilityLayer = L.geoJSON(geojson, {
                            style: function (feature) {
                                return {
                                    fillColor: feature.properties.fill,
                                    fillOpacity: feature.properties['fill-opacity'],
                                    color: feature.properties.stroke,
                                    weight: feature.properties['stroke-width'],
                                    opacity: feature.properties['stroke-opacity']
                                };
                            }
                        }).addTo(map);
                    } catch (e) {
                        console.log('Error in addGeoJsonOverlay:', e);
                    }
                }

                function addRadarMarker(lat, lon, iconPath) {
                    try {
                        var map = document.querySelector('#map')._leaflet;
                        radarMarker = L.marker([lat, lon], {
                            icon: L.icon({
                                iconUrl: iconPath,
                                iconSize: [35, 35],
                                iconAnchor: [17, 17]
                            })
                        }).bindPopup("Radar Position").addTo(map);
                    } catch (e) {
                        console.log('Error in addRadarMarker:', e);
                    }
                }

                function addRangeRings(centerLat, centerLon, maxRange, interval, color) {
                    try {
                        var map = document.querySelector('#map')._leaflet;
                        for (var radius = interval; radius <= maxRange; radius += interval) {
                            var ring = L.circle([centerLat, centerLon], {
                                radius: radius,
                                color: color,
                                fill: false,
                                weight: 1
                            }).addTo(map);
                            rangeRings.push(ring);
                        }
                    } catch (e) {
                        console.log('Error in addRangeRings:', e);
                    }
                }

            function updateMapCenter(lat, lon, zoom) {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    map.setView([lat, lon], zoom || map.getZoom());
                } catch (e) {
                    console.log('Error in updateMapCenter:', e);
                }
            }

            function clearRangeRings() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    rangeRings.forEach(function(ring) {
                        if (map.hasLayer(ring)) {
                            map.removeLayer(ring);
                        }
                    });
                    rangeRings = [];
                } catch (e) {
                    console.log('Error in clearRangeRings:', e);
                }
            }

            function clearMultipathRings() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    if (multipathLayerGroup) {
                        multipathLayerGroup.clearLayers();
                    }
                } catch (e) {
                    console.log('Error in clearMultipathRings:', e);
                }
            }

            function clearVisibilityLayer() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    if (visibilityLayer && map.hasLayer(visibilityLayer)) {
                        map.removeLayer(visibilityLayer);
                        visibilityLayer = null;
                    }
                } catch (e) {
                    console.log('Error in clearVisibilityLayer:', e);
                }
            }

            function clearSRTMOverlay() {
                try {
                    var map = document.querySelector('#map')._leaflet;
                    if (window.srtmOverlay && map.hasLayer(window.srtmOverlay)) {
                        map.removeLayer(window.srtmOverlay);
                        window.srtmOverlay = null;
                        console.log('SRTM overlay removed');
                    }
                } catch (e) {
                    console.log('Error in clearSRTMOverlay:', e);
                }
            }

                document.addEventListener('DOMContentLoaded', function() {
                    function waitForMap() {
                        var map = document.querySelector('#map');
                        if (map && map._leaflet) {
                            // Add right-click handler
                            map._leaflet.on('contextmenu', function(e) {
                                // Remove previous marker if it exists
                                if (currentMarker) {
                                    map._leaflet.removeLayer(currentMarker);
                                }
                                
                                // Create new marker
                                currentMarker = L.marker(e.latlng).addTo(map._leaflet);
                                
                                // Create and open popup
                                var popup = L.popup({
                                    closeButton: true,
                                    closeOnClick: false,
                                    autoClose: false
                                })
                                .setLatLng(e.latlng)
                                .setContent(`
                                    <div style="text-align: center;">
                                        <div style="margin-bottom: 8px;">
                                            <strong style="font-size: 14px;">Selected Location</strong><br>
                                            <span style="font-family: monospace;">
                                                Lat: ${e.latlng.lat.toFixed(6)}° | Lon: ${e.latlng.lng.toFixed(6)}°<br>
                                                HGT File: ${getHGTFilename(e.latlng.lat, e.latlng.lng)}
                                            </span>
                                        </div>
                                        <button onclick="goToLocation(${e.latlng.lat}, ${e.latlng.lng})"
                                                style="padding: 8px 12px; cursor: pointer; background-color: #4CAF50; 
                                                       color: white; border: none; border-radius: 4px;
                                                       font-size: 13px; font-weight: bold;">
                                            Go to this location
                                        </button>
                                    </div>
                                `)
                                .openOn(map._leaflet);
                                
                                // Store current popup
                                if (currentPopup) {
                                    map._leaflet.closePopup(currentPopup);
                                }
                                currentPopup = popup;
                            });

                            // Clear marker when popup is closed
                            map._leaflet.on('popupclose', function(e) {
                                if (currentMarker) {
                                    map._leaflet.removeLayer(currentMarker);
                                    currentMarker = null;
                                }
                            });
                        } else {
                            setTimeout(waitForMap, 100);
                        }
                    }
                    waitForMap();
                });

                function getHGTFilename(lat, lon) {
                    let latDeg = Math.floor(Math.abs(lat));
                    let lonDeg = Math.floor(Math.abs(lon));
                    let ns = lat >= 0 ? 'N' : 'S';
                    let ew = lon >= 0 ? 'E' : 'W';
                    return `${ns}${latDeg.toString().padStart(2, '0')}${ew}${lonDeg.toString().padStart(3, '0')}.hgt`;
                }

                function goToLocation(lat, lng) {
                    new QWebChannel(qt.webChannelTransport, function(channel) {
                        channel.objects.handler.handleMapClick(lat.toString(), lng.toString());
                    });
                }
                </script>
            """).add_to(m)
            
            # Add a marker at the location
            folium.Marker([lat, lon]).add_to(m)

            # Save map to temporary HTML file
            temp_file = 'temp_map.html'
            m.save(temp_file)
            
            # Add QWebChannel script
            with open(temp_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            webchannel_script = '<script src="qrc:///qtwebchannel/qwebchannel.js"></script>'
            content = content.replace('</head>', f'{webchannel_script}</head>')
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Load the HTML into the QWebEngineView
            self.map_view.setUrl(QUrl.fromLocalFile(os.path.abspath(temp_file)))
            
            # Reset JavaScript integration manager state
            if hasattr(self, 'js_manager'):
                self.js_manager.reset_map_ready_state()
            
            # Construct the .hgt filename
            lat_dir = "N" if lat >= 0 else "S"
            lon_dir = "E" if lon >= 0 else "W"
            lat_deg = abs(int(lat))
            lon_deg = abs(int(lon))
            hgt_filename = f"{lat_dir}{lat_deg:02d}{lon_dir}{lon_deg:03d}.hgt"
            hgt_filepath = os.path.join("srtm_data", hgt_filename)
            
            # Progress callback for download
            def download_progress(progress):
                self.results_text.append(f"Downloading SRTM data: {progress}%")
                self.results_text.verticalScrollBar().setValue(
                    self.results_text.verticalScrollBar().maximum()
                )
            
            # Check if the .hgt file exists and load/download if needed
            if os.path.exists(hgt_filepath):
                self.results_text.append(f"SRTM file found: {hgt_filename}")
                # Load the SRTM data
                self.elevation_data = srtm_handler.load_srtm_file(hgt_filepath, auto_download=False)
            else:
                self.results_text.append(f"SRTM file not found: {hgt_filename}")
                self.results_text.append("Attempting to download SRTM data...")
                # Load the SRTM data with automatic download
                self.elevation_data = srtm_handler.load_srtm_file(hgt_filepath, auto_download=True, progress_callback=download_progress)
            
            if self.elevation_data is not None:
                self.loaded_srtm_filepath = hgt_filepath
                # Enable the verify SRTM button
                self.verify_srtm_btn.setEnabled(True)
                # Get the elevation for the given coordinates
                elevation = srtm_handler.get_elevation(lat, lon, self.elevation_data, hgt_filepath)
                if elevation is not None:
                    self.results_text.append(f"SRTM file loaded: {hgt_filename}, Elevation at {lat:.6f}, {lon:.6f}: {elevation:.2f}m")
                else:
                    self.results_text.append(f"SRTM file loaded: {hgt_filename}, but elevation data not available for coordinates {lat:.6f}, {lon:.6f}")
            else:
                self.results_text.append(f"Error loading SRTM file: {hgt_filename}")
                self.results_text.append("Please check your internet connection and try again.")
                # Disable the verify SRTM button
                self.verify_srtm_btn.setEnabled(False)
            
            # Update status with formatted coordinates
            lat_dms = format_dms_string(*decimal_to_dms(lat, True))
            lon_dms = format_dms_string(*decimal_to_dms(lon, False))
            self.results_text.append(f"Navigated to: {lat_dms}, {lon_dms}")
            
        except Exception as e:
            self.results_text.append(f"Error navigating to location: {str(e)}")

    def verify_srtm_data(self):
        """Open SRTM verification dialog to inspect loaded elevation data"""
        if hasattr(self, 'loaded_srtm_filepath') and self.loaded_srtm_filepath:
            try:
                dialog = SRTMVerificationDialog(self)
                dialog.visualize_hgt(self.loaded_srtm_filepath)
                dialog.exec()
            except Exception as e:
                self.results_text.append(f"Error opening SRTM verification dialog: {str(e)}")
        else:
            self.results_text.append("No SRTM data loaded. Please go to a location first.")
    
    def load_srtm_data(self):
        """Open SRTM file selection dialog and verify data"""
        pass

    def show_settings(self):
        """Show settings dialog"""
        settings_dialog = SettingsDialog(self, self.settings)
        if settings_dialog.exec():
            self.settings = settings_dialog.get_settings()

    def show_about(self):
        """Show about dialog"""
        about_dialog = AboutDialog(self)
        about_dialog.exec()

    @pyqtSlot(str, str)
    def handleMapClick(self, lat, lon):
        """Handle location selection from the map"""
        try:
            lat_float = float(lat)
            lon_float = float(lon)
            
            # Update the coordinate inputs
            self.lat_input.setValue(lat_float)
            self.lon_input.setValue(lon_float)
            
            # Update status with formatted coordinates
            lat_dms = format_dms_string(*decimal_to_dms(lat_float, True))
            lon_dms = format_dms_string(*decimal_to_dms(lon_float, False))
            self.results_text.append(f"Selected location: {lat_dms}, {lon_dms}")
            
            # Trigger the goto_location to load elevation data
            self.goto_location()
            
        except Exception as e:
            self.results_text.append(f"Error handling map click: {str(e)}")

    @pyqtSlot(int, float, float)
    def handleMapZoom(self, zoom, lat, lon):
        """Handle map zoom events from JavaScript."""
        try:
            self.current_zoom = zoom
            # Update status bar with zoom level and center coordinates
            self.status_bar.showMessage(f"Zoom: {zoom} | Center: {lat:.6f}°, {lon:.6f}°", 2000)
            
            # Update HGT filename display if we have a location display
            hgt_filename = self.get_hgt_filename(lat, lon)
            # Could update a label here if we had one for HGT filename
            
        except Exception as e:
            print(f"Error handling map zoom event: {e}")

    @pyqtSlot(float, float)
    def handleMapPan(self, lat, lon):
        """Handle map pan events from JavaScript."""
        try:
            # Update status bar with new center coordinates
            self.status_bar.showMessage(f"Map center: {lat:.6f}°, {lon:.6f}°", 2000)
            
            # Update HGT filename for current center
            hgt_filename = self.get_hgt_filename(lat, lon)
            # Could update a label here if we had one for HGT filename
            
        except Exception as e:
            print(f"Error handling map pan event: {e}")

    def load_settings(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_settings = json.load(f)
                    
                    # Update settings while preserving defaults for any missing keys
                    self.settings.update(loaded_settings)
                    
                    # Ensure all last_parameters exist with defaults
                    default_params = {
                        'latitude': 50.314852,
                        'longitude': 23.302799,
                        'height': 30,
                        'target_height': 2,
                        'detection_distance': 3,
                        'hbeam_min': 0,
                        'hbeam_max': 360,
                        'vbeam_min': -20,
                        'vbeam_max': 20,
                        'color_pattern': "Red",
                        'opacity': 0.70,
                        'enable_rings': True,
                        'ring_interval': 1,
                        'frequency': 10.0,
                        'reflectivity': 0.5,  # Keep for backwards compatibility
                        'land_reflectivity': 0.3,
                        'water_reflectivity': 0.9,
                        'enable_multipath_rings': False,
                        'multipath_color_scheme': 'Rainbow (dB-based)',
                        'multipath_opacity': 0.6,
                        'multipath_map_opacity': 0.4,
                        'multipath_ring_style': 'Filled Polygons',
                        'gain_threshold': -10,
                        'show_nulls_only': False,
                        'show_peaks_only': False
                    }
                    
                    # Ensure all parameters exist with defaults
                    for key, default_value in default_params.items():
                        if key not in self.settings['last_parameters']:
                            self.settings['last_parameters'][key] = default_value
                            
        except Exception as e:
            print(f"Error loading settings: {e}")

    def save_settings(self):
        """Save current settings to config file."""
        try:
            # Update last parameters
            self.settings['last_parameters'].update({
                'latitude': self.lat_input.value(),
                'longitude': self.lon_input.value(),
                'height': self.height_input.value(),
                'target_height': self.target_height.value(),
                'detection_distance': self.detection_distance.value(),
                'hbeam_min': self.hbeam_min.value(),
                'hbeam_max': self.hbeam_max.value(),
                'vbeam_min': self.vbeam_min.value(),
                'vbeam_max': self.vbeam_max.value(),
                'color_pattern': self.color_pattern.currentText(),
                'opacity': self.opacity.value(),
                'enable_rings': self.enable_rings.isChecked(),
                'ring_interval': self.ring_interval.value(),
                'frequency': self.freq_input.value(),
                'reflectivity': self.land_refl_input.value(),  # Keep for backwards compatibility
                'land_reflectivity': self.land_refl_input.value(),
                'water_reflectivity': self.water_refl_input.value(),
                'enable_multipath_rings': self.enable_multipath_rings.isChecked(),
                'multipath_color_scheme': self.multipath_color_scheme.currentText(),
                'multipath_opacity': self.multipath_opacity.value(),
                'multipath_map_opacity': self.multipath_map_opacity.value(),
                'multipath_ring_style': self.multipath_ring_style.currentText(),
                'gain_threshold': self.gain_threshold.value(),
                'show_nulls_only': self.show_nulls_only.isChecked(),
                'show_peaks_only': self.show_peaks_only.isChecked(),
                'use_parallel_processing': self.enable_parallel_processing.isChecked()
            })
            
            with open(self.config_file, 'w') as f:
                json.dump(self.settings, f, indent=4)
        except Exception as e:
            print(f"Error saving settings: {e}")

    def closeEvent(self, event):
        """Override close event to save settings before closing."""
        # Stop any running threads
        try:
            if self.thread and self.thread.isRunning():
                self.thread.quit()
                self.thread.wait()  # Wait for thread to finish
        except RuntimeError:
            # Thread object has been deleted, that's fine
            pass
        
        self.save_settings()
        super().closeEvent(event)

    def trigger_analysis_update(self):
        """Restarts the timer every time a value is changed."""
        self.analysis_update_timer.start()

    def update_realtime_analysis(self):
        """Update real-time distance calculations and multipath visualization."""
        try:
            # Get current parameters
            radar_height = self.height_input.value()
            target_height = self.target_height.value()
            detection_distance = self.detection_distance.value()
            frequency = self.freq_input.value()
            reflectivity = self.land_refl_input.value() * 100  # Convert to percentage (using land reflectivity)
            vbeam_min = self.vbeam_min.value()
            vbeam_max = self.vbeam_max.value()
            
            # Calculate elevation angle from vertical beam
            elevation_angle = abs(vbeam_max - vbeam_min)
            
            # Update distance calculations
            distance_analysis = self.distance_calculator.analyze_radar_parameters(
                radar_height, elevation_angle, target_height, detection_distance, frequency
            )
            
            # Format display values
            range_text = f"{distance_analysis['range_to_target']:.2f}km" if distance_analysis['range_to_target'] else "Out of Range"
            coverage_text = "Yes" if distance_analysis['within_coverage'] else "No"
            
            # Format and display distance analysis
            summary_text = f"""=== Real-time Distance Analysis ===
Radar Height: {radar_height}m | Target Height: {target_height}m
Detection Range: {detection_distance}km | Frequency: {frequency}GHz
Elevation Angle: {elevation_angle:.1f}°

Coverage Results:
• Max Beam Height: {distance_analysis['max_beam_height']:.1f}m
• Ground Range: {distance_analysis['ground_range']:.1f}m
• Range to Target: {range_text}
• Target in Coverage: {coverage_text}
• Line of Sight Range: {distance_analysis['line_of_sight_range']:.2f}km
• Fresnel Zone Radius: {distance_analysis['fresnel_zone_radius']:.1f}m

Analysis Notes:"""
            
            for note in distance_analysis['analysis_notes']:
                summary_text += f"\n• {note}"
            
            self.distance_display.setText(summary_text)
            
            # Update multipath visualization
            if hasattr(self, 'multipath_widget'):
                self.multipath_widget.update_plot(
                    radar_height, target_height, reflectivity, frequency, detection_distance,
                    color_scheme=self.multipath_color_scheme.currentText(),
                    show_rings=self.enable_multipath_rings.isChecked(),
                    gain_threshold=self.gain_threshold.value(),
                    show_nulls_only=self.show_nulls_only.isChecked(),
                    show_peaks_only=self.show_peaks_only.isChecked()
                )
            
            # Update map rings in real-time if simulation has been run
            if hasattr(self, 'analyzer') and hasattr(self, 'map_view'):
                try:
                    # Get current location from analyzer
                    lat = self.lat_input.value()
                    lon = self.lon_input.value()
                    
                    # Update range rings if enabled
                    if self.enable_rings.isChecked():
                        self.update_range_rings_with_javascript(lat, lon, detection_distance)
                    
                    # Update multipath rings if enabled
                    if self.enable_multipath_rings.isChecked():
                        self.update_multipath_rings_with_javascript(lat, lon, detection_distance)
                        
                except Exception as e:
                    print(f"Error updating map rings in real-time: {e}")
        
        except Exception as e:
            self.distance_display.setText(f"Error in real-time analysis: {str(e)}")
            print(f"Real-time analysis error: {e}")

    def run_simulation(self):
        """Run radar coverage simulation using the 2D visibility analyzer"""
        try:
            # Get parameters from GUI
            lat = self.lat_input.value()
            lon = self.lon_input.value()
            radar_height = self.height_input.value()
            target_height = self.target_height.value()
            detection_distance = self.detection_distance.value() * 1000  # Convert to meters
            
            # Get horizontal and vertical beam angles
            hbeam_min = self.hbeam_min.value()
            hbeam_max = self.hbeam_max.value()
            vbeam_min = self.vbeam_min.value()
            vbeam_max = self.vbeam_max.value()
            
            # Get frequency and reflectivity
            frequency = self.freq_input.value()
            reflectivity = self.land_refl_input.value()  # Using land reflectivity for backwards compatibility
            
            # Get HGT filename for the current location
            hgt_filename = self.get_hgt_filename(lat, lon)
            hgt_filepath = os.path.join('srtm_data', hgt_filename)
            
            if not os.path.exists(hgt_filepath):
                self.results_text.append(f"Error: SRTM file not found: {hgt_filename}")
                return
            
            # Create visibility analyzer with HGT file and coordinates
            self.analyzer = Visibility2DAnalyzer(hgt_filepath, lat, lon)
            
            # Run simulation without creating files
            self.results_text.append("Running simulation...")
            self.status_bar.showMessage("Simulation in progress...")
            
            visibility, bounds = self.analyzer.run_analysis(
                radar_height=radar_height,
                target_height=target_height,
                max_range=detection_distance,
                h_beam_min=hbeam_min,
                h_beam_max=hbeam_max,
                v_beam_min=vbeam_min,
                v_beam_max=vbeam_max,
                frequency=frequency,
                reflectivity=reflectivity
            )
            
            # Add overlay to map using GeoJSON
            self.add_overlay_to_map(visibility, bounds, lat, lon, detection_distance)
            
            # Perform multipath analysis for simulation summary
            self.multipath_analyzer.set_frequency(frequency)
            self.multipath_analyzer.set_geometry(radar_height, target_height, reflectivity * 100)
            self.multipath_analyzer.set_analysis_range(detection_distance / 1000)
            
            # Add multipath rings to map if enabled
            self.add_multipath_rings_to_map(lat, lon, detection_distance / 1000)
            
            # Save and display the map with all elements
            temp_file = 'temp_map.html'
            self.map.save(temp_file)
            self.map_view.setUrl(QUrl.fromLocalFile(os.path.abspath(temp_file)))
            
            # Generate comprehensive simulation summary
            self.generate_simulation_summary(lat, lon, radar_height, target_height, 
                                           detection_distance / 1000, frequency, reflectivity)
            
            self.results_text.append("Simulation complete with multipath analysis!")
            self.status_bar.showMessage("Simulation complete")
            
        except Exception as e:
            self.results_text.append(f"Error running simulation: {str(e)}")
            self.status_bar.showMessage("Simulation failed")

    def run_simulation_threaded(self):
        """Starts the simulation in a background thread."""
        # Check if a thread is already running with proper error handling
        try:
            if self.thread and self.thread.isRunning():
                QMessageBox.warning(self, "Simulation in Progress", "A simulation is already running.")
                return
        except RuntimeError:
            # Thread object has been deleted, reset the reference
            self.thread = None

        # Disable UI elements to prevent concurrent modifications
        self.run_simulation_btn.setEnabled(False)
        self.export_kml_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_bar.show()
        self.status_bar.showMessage("Starting simulation...")

        # Step 1: Create a QThread and a Worker
        self.thread = QThread()
        self.worker = Worker(self.perform_simulation)
        self.worker.moveToThread(self.thread)

        # Step 2: Connect signals and slots
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.handle_simulation_results)
        self.worker.error.connect(self.handle_simulation_error)
        self.worker.progress.connect(self.update_progress)
        
        # Cleanup connections
        self.worker.finished.connect(self.thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.thread.finished.connect(self.thread.deleteLater)
        self.thread.finished.connect(self.enable_ui_elements)
        self.thread.finished.connect(self.cleanup_thread)

        # Step 3: Start the thread
        self.thread.start()

    def perform_simulation(self, progress_callback):
        """
        This function contains the actual long-running logic.
        It will be executed in the background thread.
        """
        try:
            # 1. Get parameters from GUI
            lat = self.lat_input.value()
            lon = self.lon_input.value()
            radar_height = self.height_input.value()
            target_height = self.target_height.value()
            detection_distance = self.detection_distance.value() * 1000  # meters
            hbeam_min = self.hbeam_min.value()
            hbeam_max = self.hbeam_max.value()
            vbeam_min = self.vbeam_min.value()
            vbeam_max = self.vbeam_max.value()
            frequency = self.freq_input.value()
            land_reflectivity = self.land_refl_input.value()
            water_reflectivity = self.water_refl_input.value()

            hgt_filename = self.get_hgt_filename(lat, lon)
            hgt_filepath = os.path.join('srtm_data', hgt_filename)
            if not os.path.exists(hgt_filepath):
                raise FileNotFoundError(f"SRTM file not found: {hgt_filename}")

            # 2. Run main visibility analysis
            progress_callback(5)
            analyzer = Visibility2DAnalyzer(hgt_filepath, lat, lon)
            visibility, bounds = analyzer.run_analysis(
                radar_height=radar_height,
                target_height=target_height,
                max_range=detection_distance,
                h_beam_min=hbeam_min,
                h_beam_max=hbeam_max,
                v_beam_min=vbeam_min,
                v_beam_max=vbeam_max,
                frequency=frequency,
                land_reflectivity=land_reflectivity,
                water_reflectivity=water_reflectivity,
                use_parallel_processing=self.enable_parallel_processing.isChecked()
            )
            progress_callback(85)
            # 3. Perform multipath ring analysis using the new unified method
            ring_params = {
                'effect_threshold_db': self.gain_threshold.value(),
                'color_scheme': self.multipath_color_scheme.currentText(),
                'opacity': self.multipath_map_opacity.value(),
                'show_nulls_only': self.show_nulls_only.isChecked(),
                'show_peaks_only': self.show_peaks_only.isChecked(),
                'smooth_transitions': True,
                'ring_width_km': 0.05
            }
            multipath_rings = analyzer.analyze_multipath_effects(
                radar_height, target_height, detection_distance / 1000,
                frequency, land_reflectivity, water_reflectivity, ring_params
            )
            progress_callback(100)
            return {
                'visibility': visibility,
                'bounds': bounds,
                'lat': lat,
                'lon': lon,
                'detection_distance': detection_distance,
                'analyzer': analyzer,
                'radar_height': radar_height,
                'target_height': target_height,
                'frequency': frequency,
                'land_reflectivity': land_reflectivity,
                'water_reflectivity': water_reflectivity,
                'reflectivity': land_reflectivity,  # For backwards compatibility
                'multipath_rings': multipath_rings
            }
        except Exception as e:
            raise e

    def handle_simulation_results(self, result):
        """Slot to handle the results from the worker thread using fallback method for now."""
        try:
            self.status_bar.showMessage("Simulation complete. Rendering map...", 4000)
            lat, lon = result['lat'], result['lon']
            self.analyzer = result['analyzer']
            
            # Use fallback method for now to ensure simulation works
            # 1. Create a new Folium map object
            self.map = folium.Map(location=[lat, lon], zoom_start=self.current_zoom, prefer_canvas=True)
            # 2. Add the visibility overlay
            geojson_data = self.analyzer.visibility_to_geojson(
                result['visibility'], result['bounds'],
                color_pattern=self.color_pattern.currentText(),
                opacity=self.opacity.value()
            )
            folium.GeoJson(geojson_data, style_function=lambda x: {'fillColor': x['properties']['fill'], 'fillOpacity': x['properties']['fill-opacity'], 'color': x['properties']['stroke'], 'weight': x['properties']['stroke-width'], 'opacity': x['properties']['stroke-opacity']}).add_to(self.map)
            # 3. Add other elements
            self.add_radar_marker_to_map(lat, lon)
            if self.enable_rings.isChecked():
                self.add_range_rings_to_map(lat, lon, result['detection_distance'])
            # 4. Add the pre-calculated multipath rings
            if self.enable_multipath_rings.isChecked() and result.get('multipath_rings'):
                for polygon in result['multipath_rings']:
                    folium.Polygon(
                        locations=polygon['coordinates'], color=polygon['color'], weight=1,
                        fill=True, fillColor=polygon['color'], fillOpacity=polygon['opacity'],
                        opacity=min(1.0, polygon['opacity'] * 1.5),
                        popup=folium.Popup(f"Gain: {polygon['gain_db']:.1f} dB", max_width=150)
                    ).add_to(self.map)
            # 5. Save and reload the map
            temp_file = 'temp_map.html'
            self.map.save(temp_file)
            self.map_view.setUrl(QUrl.fromLocalFile(os.path.abspath(temp_file)))
            # 6. Generate summary
            self.generate_simulation_summary(
                lat, lon, result['radar_height'], result['target_height'], 
                result['detection_distance'] / 1000, result['frequency'], result['land_reflectivity']
            )
            self.results_text.append("✅ Simulation and map rendering complete!")
            self.progress_bar.hide()
            self.status_bar.showMessage("Simulation complete.", 5000)
            
        except Exception as e:
            self.results_text.append(f"❌ Error processing simulation results: {str(e)}")
            QMessageBox.critical(self, "Processing Error", f"Failed to process simulation results:\n{e}")
            self.progress_bar.hide()
            self.status_bar.showMessage("Simulation failed.", 5000)

    def handle_simulation_error(self, error_tuple):
        """Slot to handle errors from the worker thread."""
        print("Error from worker thread:", error_tuple[2])  # Log the full traceback
        QMessageBox.critical(self, "Simulation Error", f"An error occurred: {error_tuple[1]}")
        self.progress_bar.hide()
        self.status_bar.showMessage("Simulation failed.", 5000)

    def update_progress(self, value):
        """Updates the progress bar."""
        self.progress_bar.setValue(value)

    def enable_ui_elements(self):
        """Re-enables UI elements after simulation is complete."""
        self.run_simulation_btn.setEnabled(True)
        self.export_kml_btn.setEnabled(True)

    def cleanup_thread(self):
        """Clean up thread references to prevent RuntimeError on next call."""
        self.thread = None
        self.worker = None

    def get_hgt_filename(self, lat, lon):
        """Get the HGT filename for given coordinates."""
        lat_hemisphere = "N" if lat >= 0 else "S"
        lon_hemisphere = "E" if lon >= 0 else "W"
        
        lat_val = abs(int(lat))
        lon_val = abs(int(lon))
        
        return f"{lat_hemisphere}{lat_val:02d}{lon_hemisphere}{lon_val:03d}.hgt"

    def add_radar_marker_to_map(self, lat, lon):
        """Adds the radar position marker to self.map."""
        icon_path = os.path.abspath('assets/images.jpg')
        if os.path.exists(icon_path):
            icon = folium.CustomIcon(icon_image=icon_path, icon_size=(35, 35))
        else:
            print(f"Warning: Radar icon not found at {icon_path}. Using default marker.")
            icon = folium.Icon(color='red', icon='screenshot')
            
        folium.Marker([lat, lon], popup="Radar Position", icon=icon).add_to(self.map)

    def add_range_rings_to_map(self, lat, lon, range_meters):
        """Adds distance rings to self.map."""
        interval_m = self.ring_interval.value() * 1000
        for radius in range(interval_m, int(range_meters) + interval_m, interval_m):
            folium.Circle(
                radius=radius,
                location=[lat, lon],
                color=self.color_pattern.currentText().lower(),
                fill=False,
                weight=1,
                popup=f"{radius/1000} km"
            ).add_to(self.map)

    def add_overlay_to_map(self, visibility_data, bounds, center_lat, center_lon, range_meters):
        """Add the visibility analysis overlay to the map using dynamic JavaScript updates"""
        # Get current color pattern
        color_pattern = self.color_pattern.currentText()
        
        # Create a new map or reuse existing one
        if not hasattr(self, 'map'):
            self.map = folium.Map(
                location=[center_lat, center_lon],
                zoom_start=self.current_zoom,
                prefer_canvas=True
            )
        else:
            # Clear existing layers but keep the map
            self.clear_visibility_layers()
            # Update center and zoom if needed
            self.map.location = [center_lat, center_lon]
        
        # Add radar position marker
        folium.Marker(
            [center_lat, center_lon],
            popup="Radar Position",
            icon=folium.CustomIcon(
                icon_image='assets/images.jpg',  # Adjust path as needed
                icon_size=(35, 35)
            )
        ).add_to(self.map)
        
        # Add range rings if enabled
        if self.enable_rings.isChecked():
            interval = self.ring_interval.value() * 1000  # Convert to meters
            for radius in range(interval, int(range_meters) + interval, interval):
                folium.Circle(
                    radius=radius,
                    location=[center_lat, center_lon],
                    color=color_pattern,
                    fill=False,
                    weight=1
                ).add_to(self.map)
        
        # Convert visibility data to GeoJSON and add to map
        if hasattr(self, 'analyzer'):
            geojson_data = self.analyzer.visibility_to_geojson(
                visibility_data, 
                bounds,
                color_pattern=color_pattern,
                opacity=self.opacity.value()
            )
            folium.GeoJson(
                geojson_data,
                style_function=lambda x: {
                    'fillColor': x['properties']['fill'],
                    'fillOpacity': x['properties']['fill-opacity'],
                    'color': x['properties']['stroke'],
                    'weight': x['properties']['stroke-width'],
                    'opacity': x['properties']['stroke-opacity']
                }
            ).add_to(self.map)

    def update_map_with_javascript(self, visibility_data, bounds, center_lat, center_lon, range_meters):
        """Update the map using JavaScript calls for better performance"""
        try:
            # Get current color pattern
            color_pattern = self.color_pattern.currentText()
            
            # Prepare GeoJSON data
            geojson_data = None
            if hasattr(self, 'analyzer'):
                geojson_data = self.analyzer.visibility_to_geojson(
                    visibility_data, 
                    bounds,
                    color_pattern=color_pattern,
                    opacity=self.opacity.value()
                )
            
            # Check if JavaScript functions are available before calling them
            check_functions_js = """
                typeof clearLayers !== 'undefined' && 
                typeof addGeoJsonOverlay !== 'undefined' && 
                typeof addRadarMarker !== 'undefined' && 
                typeof addRangeRings !== 'undefined' && 
                typeof updateMapCenter !== 'undefined'
            """
            
            def execute_with_fallback():
                # Use a more robust approach - fallback to original method
                print("JavaScript functions not available, using fallback method")
                self.add_overlay_to_map(visibility_data, bounds, center_lat, center_lon, range_meters)
            
            def execute_javascript_updates(result):
                if result:
                    # Update map center
                    js_call = f"updateMapCenter({center_lat}, {center_lon});"
                    self.map_view.page().runJavaScript(js_call)
                    
                    # Clear existing layers
                    self.map_view.page().runJavaScript("clearLayers();")
                    
                    # Add radar marker
                    radar_icon_path = os.path.abspath('assets/images.jpg').replace('\\', '/')
                    js_call = f"addRadarMarker({center_lat}, {center_lon}, 'file:///{radar_icon_path}');"
                    self.map_view.page().runJavaScript(js_call)
                    
                    # Add range rings
                    if self.enable_rings.isChecked():
                        interval = self.ring_interval.value() * 1000  # Convert to meters
                        js_call = f"addRangeRings({center_lat}, {center_lon}, {range_meters}, {interval}, '{color_pattern}');"
                        self.map_view.page().runJavaScript(js_call)
                    
                    # Add GeoJSON overlay
                    if geojson_data:
                        geojson_str = json.dumps(geojson_data)
                        js_call = f"addGeoJsonOverlay({geojson_str});"
                        self.map_view.page().runJavaScript(js_call)
                    
                    # Add color legend
                    js_add_legend = f"addColorLegend('{color_pattern}', 'bottomright');"
                    self.map_view.page().runJavaScript(js_add_legend)
                else:
                    execute_with_fallback()
            
            # Check if functions are available and execute accordingly
            self.map_view.page().runJavaScript(check_functions_js, execute_javascript_updates)
            
        except Exception as e:
            print(f"Error updating map with JavaScript: {e}")
            # Fall back to the original method if JavaScript fails
            self.add_overlay_to_map(visibility_data, bounds, center_lat, center_lon, range_meters)

    def clear_visibility_layers(self):
        """Clear existing visibility layers from the map"""
        if hasattr(self, 'map'):
            # Store current state
            center = self.map.location
            # Create new map with same state
            self.map = folium.Map(
                location=center,
                zoom_start=self.current_zoom,
                prefer_canvas=True
            )

    def add_multipath_rings_to_map(self, center_lat, center_lon, max_range_km):
        """Add multipath interference rings to the map visualization with polygon support."""
        try:
            if not hasattr(self, 'multipath_analyzer') or not hasattr(self, 'map'):
                return
            
            # Check if multipath rings are enabled
            if not self.enable_multipath_rings.isChecked():
                return
            
            ring_style = self.multipath_ring_style.currentText()
            map_opacity = self.multipath_map_opacity.value()
            
            # Calculate multipath ring polygons
            multipath_polygons = self.multipath_analyzer.calculate_multipath_polygons(
                center_lat=center_lat,
                center_lon=center_lon,
                max_range_km=max_range_km,
                effect_threshold_db=self.gain_threshold.value(),
                color_scheme=self.multipath_color_scheme.currentText(),
                opacity=map_opacity,
                show_nulls_only=self.show_nulls_only.isChecked(),
                show_peaks_only=self.show_peaks_only.isChecked(),
                smooth_transitions=True,
                ring_width_km=0.05
            )
            
            # Add polygons to map based on style
            for polygon in multipath_polygons:
                # IMPORTANT: Define variables before they are used.
                coordinates = polygon['coordinates']
                color = polygon['color']
                opacity = polygon['opacity']
                gain_db = polygon['gain_db']
                effect_type = polygon['effect_type']

                # Create popup text
                popup_text = f"""
                <div style="font-family: Arial, sans-serif; min-width: 200px;">
                    <h4 style="margin: 0 0 10px 0; color: #333;">Multipath Ring</h4>
                    <div style="margin: 5px 0;"><b>Distance:</b> {polygon['distance_km']:.2f} km</div>
                    <div style="margin: 5px 0;"><b>Effect:</b> {effect_type.title()}</div>
                    <div style="margin: 5px 0;"><b>Gain:</b> {gain_db:.1f} dB</div>
                    <div style="margin: 5px 0;"><b>Type:</b> {'Enhancement' if gain_db > 0 else 'Nulling'}</div>
                    <div style="margin: 5px 0;"><b>Inner Radius:</b> {polygon['inner_radius_km']:.3f} km</div>
                    <div style="margin: 5px 0;"><b>Outer Radius:</b> {polygon['outer_radius_km']:.3f} km</div>
                </div>
                """
                
                # Add filled polygon if style includes polygons
                if ring_style in ['Filled Polygons', 'Both']:
                    folium.Polygon(
                        locations=coordinates,
                        color=color,
                        weight=1,
                        fill=True,
                        fillColor=color,
                        fillOpacity=opacity,
                        opacity=min(1.0, opacity * 1.5),  # Slightly more opaque border
                        popup=folium.Popup(popup_text, max_width=300)
                    ).add_to(self.map)
                
                # Add outline circle if style includes outlines
                if ring_style in ['Outlines Only', 'Both']:
                    # Add outer circle
                    folium.Circle(
                        location=[center_lat, center_lon],
                        radius=polygon['outer_radius_km'] * 1000,  # Convert to meters
                        color=color,
                        weight=2,
                        fill=False,
                        opacity=opacity,
                        popup=folium.Popup(popup_text, max_width=300)
                    ).add_to(self.map)
                    
                    # Add inner circle if it exists (for donut shape)
                    if polygon['inner_radius_km'] > 0:
                        folium.Circle(
                            location=[center_lat, center_lon],
                            radius=polygon['inner_radius_km'] * 1000,
                            color=color,
                            weight=2,
                            fill=False,
                            opacity=opacity
                        ).add_to(self.map)
                
        except Exception as e:
            print(f"Error adding multipath rings: {e}")

    def update_range_rings_with_javascript(self, center_lat, center_lon, max_range_km):
        """Update range rings using JavaScript for better performance"""
        try:
            if not self.enable_rings.isChecked():
                return
            
            # Clear existing range rings safely
            clear_js = """
            if (typeof clearRangeRings === 'function') {
                clearRangeRings();
            } else {
                console.log('clearRangeRings function not available');
            }
            """
            self.js_manager.safe_javascript_call(clear_js)
            
            # Get ring parameters
            interval = self.ring_interval.value() * 1000  # Convert to meters
            color_pattern = self.color_pattern.currentText()
            max_range_meters = max_range_km * 1000
            
            # Create range rings data
            rings_data = []
            for radius in range(interval, int(max_range_meters) + interval, interval):
                rings_data.append({
                    'center': [center_lat, center_lon],
                    'radius': radius,
                    'color': color_pattern,
                    'weight': 1,
                    'fill': False
                })
            
            # Add range rings using JavaScript safely
            if rings_data:
                rings_str = json.dumps(rings_data)
                js_call = f"""
                try {{
                    var map = document.querySelector('#map')._leaflet;
                    if (map) {{
                        var rings = {rings_str};
                        rings.forEach(function(ring) {{
                            var circle = L.circle(ring.center, {{
                                radius: ring.radius,
                                color: ring.color,
                                fill: ring.fill,
                                weight: ring.weight
                            }}).addTo(map);
                            rangeRings.push(circle);
                        }});
                    }}
                }} catch (e) {{
                    console.log('Error adding range rings:', e);
                }}
                """
                self.js_manager.safe_javascript_call(js_call)
                
        except Exception as e:
            print(f"Error updating range rings with JavaScript: {e}")
            # Fall back to the original method if JavaScript fails
            self.add_range_rings_to_map(center_lat, center_lon, max_range_km * 1000)

    def update_multipath_rings_with_javascript(self, center_lat, center_lon, max_range_km):
        """Update multipath rings using JavaScript for better performance"""
        try:
            if not hasattr(self, 'multipath_analyzer'):
                return
            
            # Check if multipath rings are enabled
            if not self.enable_multipath_rings.isChecked():
                return
            
            ring_style = self.multipath_ring_style.currentText()
            map_opacity = self.multipath_map_opacity.value()
            
            # Calculate multipath ring polygons
            multipath_polygons = self.multipath_analyzer.calculate_multipath_polygons(
                center_lat=center_lat,
                center_lon=center_lon,
                max_range_km=max_range_km,
                effect_threshold_db=self.gain_threshold.value(),
                color_scheme=self.multipath_color_scheme.currentText(),
                opacity=map_opacity,
                show_nulls_only=self.show_nulls_only.isChecked(),
                show_peaks_only=self.show_peaks_only.isChecked(),
                smooth_transitions=True,
                ring_width_km=0.05
            )
            
            # Prepare rings data for JavaScript
            rings_data = []
            for polygon in multipath_polygons:
                coordinates = polygon['coordinates']
                color = polygon['color']
                opacity = polygon['opacity']
                gain_db = polygon['gain_db']
                effect_type = polygon['effect_type']

                # Create popup text
                popup_text = f"""
                <div style="font-family: Arial, sans-serif; min-width: 200px;">
                    <h4 style="margin: 0 0 10px 0; color: #333;">Multipath Ring</h4>
                    <div style="margin: 5px 0;"><b>Distance:</b> {polygon['distance_km']:.2f} km</div>
                    <div style="margin: 5px 0;"><b>Effect:</b> {effect_type.title()}</div>
                    <div style="margin: 5px 0;"><b>Gain:</b> {gain_db:.1f} dB</div>
                    <div style="margin: 5px 0;"><b>Type:</b> {'Enhancement' if gain_db > 0 else 'Nulling'}</div>
                    <div style="margin: 5px 0;"><b>Inner Radius:</b> {polygon['inner_radius_km']:.3f} km</div>
                    <div style="margin: 5px 0;"><b>Outer Radius:</b> {polygon['outer_radius_km']:.3f} km</div>
                </div>
                """
                
                # Add filled polygon if style includes polygons
                if ring_style in ['Filled Polygons', 'Both']:
                    rings_data.append({
                        'type': 'polygon',
                        'coordinates': coordinates,
                        'color': color,
                        'weight': 1,
                        'fill': True,
                        'fillColor': color,
                        'fillOpacity': opacity,
                        'opacity': min(1.0, opacity * 1.5),
                        'popup': popup_text
                    })
                
                # Add outline circle if style includes outlines
                if ring_style in ['Outlines Only', 'Both']:
                    # Add outer circle
                    rings_data.append({
                        'type': 'circle',
                        'center': [center_lat, center_lon],
                        'radius': polygon['outer_radius_km'] * 1000,
                        'color': color,
                        'weight': 2,
                        'fill': False,
                        'opacity': opacity,
                        'popup': popup_text
                    })
                    
                    # Add inner circle if it exists (for donut shape)
                    if polygon['inner_radius_km'] > 0:
                        rings_data.append({
                            'type': 'circle',
                            'center': [center_lat, center_lon],
                            'radius': polygon['inner_radius_km'] * 1000,
                            'color': color,
                            'weight': 2,
                            'fill': False,
                            'opacity': opacity
                        })
            
            # Use the safe JavaScript call method
            if rings_data:
                rings_str = json.dumps(rings_data)
                js_call = f"""
                if (typeof addMultipathRings === 'function') {{
                    addMultipathRings({rings_str});
                }} else {{
                    console.log('addMultipathRings function not available');
                }}
                """
                self.js_manager.safe_javascript_call(js_call)
            
        except Exception as e:
            print(f"Error updating multipath rings with JavaScript: {e}")
            # Fall back to the original method if JavaScript fails
            self.add_multipath_rings_to_map(center_lat, center_lon, max_range_km)

    def generate_simulation_summary(self, lat, lon, radar_height, target_height, 
                                  max_range_km, frequency, reflectivity):
        """Generate a comprehensive simulation summary including distance and multipath analysis."""
        try:
            # Get distance analysis
            elevation_angle = abs(self.vbeam_max.value() - self.vbeam_min.value())
            distance_analysis = self.distance_calculator.analyze_radar_parameters(
                radar_height, elevation_angle, target_height, max_range_km, frequency
            )
            
            # Get multipath analysis summary
            multipath_summary = self.multipath_analyzer.get_multipath_summary()
            
            # Format comprehensive summary
            summary_text = f"""
=== COMPREHENSIVE SIMULATION SUMMARY ===
Location: {lat:.6f}°, {lon:.6f}°
Timestamp: {self.get_current_timestamp()}

=== GEOMETRY PARAMETERS ===
• Radar Height: {radar_height}m AGL
• Target Height: {target_height}m AGL  
• Detection Range: {max_range_km}km
• Frequency: {frequency}GHz
• Reflectivity: {reflectivity*100:.1f}%
• Beam Coverage: {self.hbeam_min.value()}° to {self.hbeam_max.value()}° (H), {self.vbeam_min.value()}° to {self.vbeam_max.value()}° (V)

=== DISTANCE ANALYSIS ===
• Line-of-Sight Range: {distance_analysis['line_of_sight_range']:.2f}km
• Max Beam Height: {distance_analysis['max_beam_height']:.1f}m
• Ground Range: {distance_analysis['ground_range']:.1f}m
• Fresnel Zone Radius: {distance_analysis['fresnel_zone_radius']:.1f}m
• Target Coverage: {"✓ DETECTED" if distance_analysis['within_coverage'] else "✗ OUT OF RANGE"}

=== MULTIPATH ANALYSIS ===
• Multipath Nulls: {multipath_summary['null_count']} locations
• Multipath Peaks: {multipath_summary['peak_count']} locations
• Dynamic Range: {multipath_summary['statistics']['dynamic_range_db']:.1f}dB
• Max Gain: {multipath_summary['statistics']['max_gain_db']:.1f}dB
• Min Gain: {multipath_summary['statistics']['min_gain_db']:.1f}dB"""
            
            if multipath_summary['nulls_km']:
                summary_text += f"\n• First Null: {multipath_summary['nulls_km'][0]:.2f}km"
            if multipath_summary['peaks_km']:
                summary_text += f"\n• First Peak: {multipath_summary['peaks_km'][0]:.2f}km"
            
            # Add analysis warnings
            if distance_analysis['analysis_notes']:
                summary_text += "\n\n=== ANALYSIS WARNINGS ==="
                for note in distance_analysis['analysis_notes']:
                    summary_text += f"\n• {note}"
            
            summary_text += f"\n\n=== COVERAGE SUMMARY ==="
            summary_text += f"\nVisibility analysis completed with {len(multipath_summary['nulls_km']) + len(multipath_summary['peaks_km'])} multipath effects detected."
            
            # Display summary
            self.results_text.append(summary_text)
            
        except Exception as e:
            self.results_text.append(f"Error generating simulation summary: {str(e)}")
            
    def get_current_timestamp(self):
        """Get current timestamp for simulation summaries."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def update_map(self, visibility=None, bounds=None):
        """Update the map with visibility analysis results."""
        if visibility is None or bounds is None:
            return
            
        # Get current color pattern and convert to RGB
        color_pattern = self.color_pattern.currentText()
        if hasattr(self, 'analyzer'):
            color = self.analyzer._get_color_for_pattern(color_pattern)
            rgb_color = color['rgb']
            
        # Create RGBA data
        rgba_data = np.zeros((*visibility.shape, 4))
        
        # Fill with selected color
        rgba_data[visibility, 0] = rgb_color[0]  # R
        rgba_data[visibility, 1] = rgb_color[1]  # G
        rgba_data[visibility, 2] = rgb_color[2]  # B
        rgba_data[visibility, 3] = 0.2  # Alpha
        
        # Find contours
        contours = measure.find_contours(visibility, 0.5)
        
        # Convert bounds to folium coordinates
        south, north = bounds[2], bounds[3]
        west, east = bounds[0], bounds[1]
        
        # Create the base overlay
        img = Image.fromarray((rgba_data * 255).astype(np.uint8))
        
        # Create a drawing context
        draw = ImageDraw.Draw(img)
        
        # Draw contours with selected color
        rgb_int = tuple(int(x * 255) for x in rgb_color)
        for contour in contours:
            # Convert contour coordinates to image coordinates
            y_coords = contour[:, 0] / visibility.shape[0] * img.height
            x_coords = contour[:, 1] / visibility.shape[1] * img.width
            points = list(zip(x_coords, y_coords))
            draw.line(points, fill=rgb_int, width=1)
        
        # Save temporary image
        img_path = "temp_overlay.png"
        img.save(img_path)
        
        # Add image overlay to map
        bounds = [[south, west], [north, east]]
        image_overlay = folium.raster_layers.ImageOverlay(
            img_path,
            bounds=bounds,
            opacity=1.0,
            name="Visibility"
        )
        
        # Clear previous layers and add new overlay
        self.clear_visibility_layers()
        image_overlay.add_to(self.map)
        
        # Remove temporary file
        os.remove(img_path)
        
        # Update the map display
        self.update_map_display()

    def on_color_pattern_changed(self):
        """Handle color pattern change."""
        if hasattr(self, 'analyzer') and hasattr(self, 'last_analysis'):
            # Get current visibility data and bounds
            visibility = self.last_analysis['visibility']
            bounds = self.last_analysis['bounds']
            range_meters = self.last_analysis['radius_km'] * 1000
            
            # Update legend with new color pattern
            color_pattern = self.color_pattern.currentText()
            js_update_legend = f"addColorLegend('{color_pattern}', 'bottomright');"
            self.map_view.page().runJavaScript(js_update_legend)
            
            # Re-add overlay with new color
            self.add_overlay_to_map(
                visibility,
                bounds,
                self.analyzer.observer_lat,
                self.analyzer.observer_lon,
                range_meters
            )

    def export_kml(self):
        if not hasattr(self, 'analyzer') or not hasattr(self.analyzer, 'last_analysis'):
            self.results_text.append("Error: No simulation results found. Please run a simulation first.")
            return
            
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Export KML",
            "",
            "KML Files (*.kml)"
        )
        
        if filename:
            try:
                print(f"UI DEBUG: Starting KML export to {filename}")
                
                # Get analysis data
                analysis = self.analyzer.last_analysis
                
                # Prepare multipath parameters if multipath rings are enabled
                multipath_params = None
                if self.enable_multipath_rings.isChecked():
                    multipath_params = {
                        'max_range_km': analysis['radius_km'],
                        'effect_threshold_db': self.gain_threshold.value(),
                        'color_scheme': self.multipath_color_scheme.currentText(),
                        'opacity': self.multipath_map_opacity.value(),
                        'show_nulls_only': self.show_nulls_only.isChecked(),
                        'show_peaks_only': self.show_peaks_only.isChecked(),
                        'smooth_transitions': True
                    }

                print(f"UI DEBUG: Calling export_to_kml with parameters:")
                print(f"  - visibility shape: {analysis['visibility'].shape}")
                print(f"  - bounds: {analysis['bounds']}")
                print(f"  - enable_rings: {self.enable_rings.isChecked()}")
                print(f"  - opacity: {self.opacity.value()}")
                print(f"  - color_pattern: {self.color_pattern.currentText()}")

                # Export KML with range ring settings, opacity and color pattern
                # Let the export_to_kml method handle both PNG and KML creation
                self.analyzer.export_to_kml(
                    analysis['visibility'],
                    analysis['bounds'],
                    filename,
                    enable_rings=self.enable_rings.isChecked(),
                    ring_interval=self.ring_interval.value(),
                    detection_distance=self.detection_distance.value(),
                    opacity=self.opacity.value(),  # Pass opacity value from GUI
                    color_pattern=self.color_pattern.currentText(),  # Pass selected color pattern
                    enable_multipath_rings=self.enable_multipath_rings.isChecked(),
                    multipath_analyzer=self.multipath_analyzer,
                    multipath_params=multipath_params
                )
                
                # Check if files were actually created
                save_dir = os.path.dirname(filename)
                base_name = os.path.splitext(os.path.basename(filename))[0]
                overlay_file = os.path.join(save_dir, f"{base_name}_overlay.png")
                
                if os.path.exists(filename):
                    print(f"UI DEBUG: KML file created successfully: {filename}")
                    self.results_text.append(f"KML file exported: {filename}")
                else:
                    print(f"UI DEBUG: KML file NOT created: {filename}")
                    self.results_text.append(f"Error: KML file was not created at {filename}")
                    
                if os.path.exists(overlay_file):
                    print(f"UI DEBUG: Overlay PNG created successfully: {overlay_file}")
                    self.results_text.append(f"Overlay PNG exported: {overlay_file}")
                else:
                    print(f"UI DEBUG: Overlay PNG NOT created: {overlay_file}")
                    self.results_text.append(f"Error: Overlay PNG was not created at {overlay_file}")
                
                self.results_text.append(f"Export process completed for: {save_dir}")
                self.status_bar.showMessage("KML export complete")
            except Exception as e:
                print(f"UI DEBUG: Exception during export: {str(e)}")
                import traceback
                traceback.print_exc()
                self.results_text.append(f"Error exporting files: {str(e)}")
                self.status_bar.showMessage("Export failed")

    def export_csv(self):
        """Export analysis results to CSV file"""
        try:
            from exports.csv_exporter import export_to_csv
            
            # Check if we have analysis data
            if not hasattr(self, 'distance_calculator') or not hasattr(self, 'multipath_analyzer'):
                self.results_text.append("No analysis data available. Please run a simulation first.")
                return
            
            # Get current parameters
            lat = self.lat_input.value()
            lon = self.lon_input.value()
            radar_height = self.height_input.value()
            target_height = self.target_height_input.value()
            max_range_km = self.detection_distance_input.value()
            frequency = self.frequency_input.value()
            land_reflectivity = self.land_reflectivity_input.value()
            water_reflectivity = self.water_reflectivity_input.value()
            
            # Create output filename
            timestamp = self.get_current_timestamp()
            output_file = f"exports/analysis_results_{timestamp}.csv"
            
            # Prepare data for export
            data_dict = {
                'simulation_parameters': {
                    'latitude': lat,
                    'longitude': lon,
                    'height': radar_height,
                    'target_height': target_height,
                    'detection_distance': max_range_km,
                    'frequency': frequency,
                    'land_reflectivity': land_reflectivity,
                    'water_reflectivity': water_reflectivity
                },
                'distance_analysis': self.distance_calculator.last_calculation or {},
                'multipath_analysis': self.multipath_analyzer.get_multipath_summary() or {}
            }
            
            # Export to CSV
            success = export_to_csv(data_dict, output_file)
            
            if success:
                self.results_text.append(f"CSV export completed: {output_file}")
            else:
                self.results_text.append("Error exporting CSV file.")
                
        except Exception as e:
            self.results_text.append(f"Error exporting CSV: {str(e)}")

    def export_geojson(self):
        """Export analysis results to GeoJSON file"""
        try:
            from exports.geojson_exporter import export_combined_geojson
            
            # Check if we have simulation results
            if not hasattr(self, 'last_simulation_result') or self.last_simulation_result is None:
                self.results_text.append("No simulation results available. Please run a simulation first.")
                return
            
            # Get current parameters
            lat = self.lat_input.value()
            lon = self.lon_input.value()
            radar_height = self.height_input.value()
            target_height = self.target_height_input.value()
            max_range_km = self.detection_distance_input.value()
            frequency = self.frequency_input.value()
            land_reflectivity = self.land_reflectivity_input.value()
            water_reflectivity = self.water_reflectivity_input.value()
            color_pattern = self.color_pattern_combo.currentText()
            opacity = self.opacity_slider.value() / 100.0
            
            # Create output filename
            timestamp = self.get_current_timestamp()
            output_file = f"exports/viewshed_analysis_{timestamp}.geojson"
            
            # Extract simulation results
            visibility_data = self.last_simulation_result.get('visibility_data')
            bounds = self.last_simulation_result.get('bounds')
            ring_polygons = self.last_simulation_result.get('ring_polygons', [])
            
            if visibility_data is None or bounds is None:
                self.results_text.append("Invalid simulation results. Please run a simulation first.")
                return
            
            # Prepare metadata
            metadata = {
                "generator": "Blighter Viewshed Analysis Tool",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0",
                "parameters": {
                    'latitude': lat,
                    'longitude': lon,
                    'height': radar_height,
                    'target_height': target_height,
                    'detection_distance': max_range_km,
                    'frequency': frequency,
                    'land_reflectivity': land_reflectivity,
                    'water_reflectivity': water_reflectivity
                },
                "description": "Radar viewshed analysis results exported as GeoJSON"
            }
            
            # Export combined GeoJSON
            success = export_combined_geojson(
                viewshed_data=visibility_data,
                bounds=bounds,
                observer_lat=lat,
                observer_lon=lon,
                rings_data=ring_polygons,
                filename=output_file,
                color_pattern=color_pattern,
                opacity=opacity,
                metadata=metadata
            )
            
            if success:
                self.results_text.append(f"GeoJSON export completed: {output_file}")
            else:
                self.results_text.append("Error exporting GeoJSON file.")
                
        except Exception as e:
            self.results_text.append(f"Error exporting GeoJSON: {str(e)}")

    def extract_srtm(self):
        """Render the currently loaded SRTM elevation data as a terrain overlay on the map."""
        try:
            # Check if SRTM data is loaded
            if not hasattr(self, 'elevation_data') or self.elevation_data is None:
                self.results_text.append("Error: No SRTM data loaded. Please click 'Go to Location and load elevation data' first.")
                self.status_bar.showMessage("No SRTM data available - go to location first")
                return
                
            if not hasattr(self, 'loaded_srtm_filepath') or not self.loaded_srtm_filepath:
                self.results_text.append("Error: No SRTM file path available. Please click 'Go to Location and load elevation data' first.")
                self.status_bar.showMessage("No SRTM file path - go to location first")
                return
            
            self.results_text.append("Extracting SRTM data to map...")
            self.status_bar.showMessage("Extracting SRTM data...")
            
            # Debug information
            self.results_text.append(f"Debug: elevation_data shape = {self.elevation_data.shape}")
            self.results_text.append(f"Debug: loaded_srtm_filepath = {self.loaded_srtm_filepath}")
            
            # Get coordinates from filename to determine bounds
            from utils.coordinate_utils import get_srtm_bounds
            filename = os.path.basename(self.loaded_srtm_filepath)
            
            # Extract SW corner coordinates from filename (e.g., 'N50W004.hgt')
            lat = int(filename[1:3])
            lon = int(filename[4:7])
            if filename[0] == 'S':
                lat = -lat
            if filename[3] == 'W':
                lon = -lon
                
            # Get tile bounds
            north, south, east, west = get_srtm_bounds(lat, lon)
            
            # Create hillshaded terrain image
            import matplotlib.pyplot as plt
            from matplotlib.colors import LightSource
            
            # Create figure for terrain visualization
            fig = plt.figure(figsize=(10, 10))
            ax = fig.add_subplot(111)
            
            # Create hillshade
            ls = LightSource(azdeg=315, altdeg=45)
            hillshade = ls.hillshade(self.elevation_data, vert_exag=1.0)
            
            # Plot terrain with hillshade overlay
            ax.imshow(self.elevation_data, cmap='terrain', origin='upper')
            ax.imshow(hillshade, cmap='gray', alpha=0.3, origin='upper')
            
            ax.set_axis_off()
            plt.subplots_adjust(top=1, bottom=0, right=1, left=0, hspace=0, wspace=0)
            plt.margins(0, 0)
            
            # Save temporary terrain image
            temp_img_path = "temp_srtm_terrain.png"
            fig.savefig(temp_img_path, 
                       bbox_inches='tight',
                       pad_inches=0,
                       dpi=300,
                       format='png')
            plt.close(fig)
            
            # Add image overlay to map using JavaScript
            bounds = [[south, west], [north, east]]
            
            # Create JavaScript code to add the terrain overlay
            js_code = f"""
            try {{
                var map = document.querySelector('#map')._leaflet;
                
                // Clear any existing SRTM overlay
                if (window.srtmOverlay) {{
                    map.removeLayer(window.srtmOverlay);
                }}
                
                // Create new image overlay
                window.srtmOverlay = L.imageOverlay('{temp_img_path}', {{
                    opacity: 0.7,
                    interactive: true
                }}).addTo(map);
                
                // Add popup with terrain info
                window.srtmOverlay.on('click', function() {{
                    var popup = L.popup()
                        .setLatLng([{(north + south) / 2}, {(east + west) / 2}])
                        .setContent('<b>SRTM Terrain Data</b><br>' +
                                   'File: {filename}<br>' +
                                   'Bounds: N:{north:.4f}° S:{south:.4f}° E:{east:.4f}° W:{west:.4f}°<br>' +
                                   'Resolution: {self.elevation_data.shape[0]}x{self.elevation_data.shape[1]} points<br>' +
                                   'Click to remove overlay')
                        .openOn(map);
                }});
                
                console.log('SRTM terrain overlay added successfully');
            }} catch (e) {{
                console.log('Error adding SRTM overlay:', e);
            }}
            """
            
            # Execute JavaScript to add overlay
            self.map_view.page().runJavaScript(js_code)
            
            # Calculate and display terrain statistics
            valid_elevations = self.elevation_data[self.elevation_data != -32768]
            min_elev = np.min(valid_elevations)
            max_elev = np.max(valid_elevations)
            mean_elev = np.mean(valid_elevations)
            
            self.results_text.append(f"SRTM terrain overlay added successfully!")
            self.results_text.append(f"File: {filename}")
            self.results_text.append(f"Elevation range: {min_elev:.1f}m to {max_elev:.1f}m")
            self.results_text.append(f"Mean elevation: {mean_elev:.1f}m")
            self.results_text.append(f"Resolution: {self.elevation_data.shape[0]}x{self.elevation_data.shape[1]} points")
            self.results_text.append("Click on the terrain overlay to remove it or view details.")
            
            self.status_bar.showMessage("SRTM terrain overlay added - click overlay to remove")
            
        except Exception as e:
            self.results_text.append(f"Error extracting SRTM data to map: {str(e)}")
            self.status_bar.showMessage("SRTM extraction failed")
            import traceback
            print(f"Full error in extract_srtm: {traceback.format_exc()}")

    def export_srtm_kml(self):
        if hasattr(self, 'loaded_srtm_filepath') and self.loaded_srtm_filepath:
            verify_dialog = SRTMVerificationDialog(self)
            verify_dialog.visualize_hgt(self.loaded_srtm_filepath)
            verify_dialog.exec()
            self.status_bar.showMessage(f"Opened SRTM verification dialog for: {os.path.basename(self.loaded_srtm_filepath)}")
        else:
            self.status_bar.showMessage("No SRTM file loaded. Please go to a location first.")

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
