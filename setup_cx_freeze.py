
import sys
from cx_Freeze import setup, Executable

# Build options
build_options = {
    "packages": [
        "PyQt6", "numpy", "scipy", "matplotlib", "PIL", "rasterio", 
        "shapely", "folium", "simplekml", "skimage", "interface", 
        "elevation", "utils", "viewshed", "mapping"
    ],
    "excludes": ["tkinter", "test", "unittest", "distutils"],
    "include_files": [
        ("config.example.json", "config.example.json"),
        ("assets/", "assets/"),
        ("srtm_data/", "srtm_data/"),
        ("output/", "output/"),
        ("exports/", "exports/"),
    ],
    "zip_include_packages": "*",
    "zip_exclude_packages": [],
    "optimize": 2,
}

# Base for Windows GUI application
base = None
if sys.platform == "win32":
    base = "Win32GUI"

# Executable configuration
executables = [
    Executable(
        "main.py",
        base=base,
        target_name="Radar_Analysis_Tool.exe",
        icon="assets/radar_icon.ico" if os.path.exists("assets/radar_icon.ico") else None,
        shortcut_name="Radar Analysis Tool",
        shortcut_dir="DesktopFolder",
    )
]

# Setup configuration
setup(
    name="Radar Analysis Tool",
    version="1.0.0",
    description="Professional radar analysis and viewshed calculation tool",
    author="Radar Analysis Tool Development Team",
    options={"build_exe": build_options},
    executables=executables,
)
