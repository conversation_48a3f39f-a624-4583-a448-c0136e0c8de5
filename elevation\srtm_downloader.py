# SRTM Data Downloader
#
# Responsibilities:
# - Download SRTM .hgt files from NASA servers
# - Handle authentication and file management
# - Provide progress feedback during downloads
# - Cache downloaded files locally
#
# Key Functions:
# - download_srtm_tile(lat, lon, progress_callback=None) -> str
# - get_srtm_url(lat, lon) -> str
# - validate_downloaded_file(filepath) -> bool

import os
import requests
import urllib.parse
from pathlib import Path
import time
from typing import Optional, Callable

class SRTMDownloader:
    """Downloads SRTM elevation data files from NASA servers."""
    
    def __init__(self, cache_dir: str = "srtm_data"):
        """
        Initialize the SRTM downloader.
        
        Args:
            cache_dir: Directory to store downloaded SRTM files
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # NASA Earthdata servers
        self.base_url = "https://e4ftl01.cr.usgs.gov/MEASURES/SRTMGL1.003/2000.02.11"
        
        # Session for downloads
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'BlighterViewshedAnalysisTool/1.0'
        })
    
    def get_srtm_filename(self, lat: float, lon: float) -> str:
        """
        Generate SRTM filename for given coordinates.
        
        Args:
            lat: Latitude in decimal degrees
            lon: Longitude in decimal degrees
            
        Returns:
            SRTM filename (e.g., 'N50W004.SRTMGL1.hgt')
        """
        lat_dir = "N" if lat >= 0 else "S"
        lon_dir = "E" if lon >= 0 else "W"
        lat_deg = abs(int(lat))
        lon_deg = abs(int(lon))
        
        return f"{lat_dir}{lat_deg:02d}{lon_dir}{lon_deg:03d}.SRTMGL1.hgt"
    
    def get_srtm_url(self, lat: float, lon: float) -> str:
        """
        Generate download URL for SRTM tile.
        
        Args:
            lat: Latitude in decimal degrees
            lon: Longitude in decimal degrees
            
        Returns:
            Full download URL for the SRTM tile
        """
        filename = self.get_srtm_filename(lat, lon)
        return f"{self.base_url}/{filename}"
    
    def get_local_filepath(self, lat: float, lon: float) -> Path:
        """
        Get local filepath for SRTM tile.
        
        Args:
            lat: Latitude in decimal degrees
            lon: Longitude in decimal degrees
            
        Returns:
            Local filepath for the SRTM tile
        """
        filename = self.get_srtm_filename(lat, lon)
        return self.cache_dir / filename
    
    def validate_downloaded_file(self, filepath: Path) -> bool:
        """
        Validate that downloaded file is a valid SRTM file.
        
        Args:
            filepath: Path to the downloaded file
            
        Returns:
            True if file is valid, False otherwise
        """
        try:
            if not filepath.exists():
                return False
            
            # Check file size (SRTM files should be ~2.8MB)
            file_size = filepath.stat().st_size
            if file_size < 2800000 or file_size > 3000000:
                return False
            
            # Try to read first few bytes to check format
            with open(filepath, 'rb') as f:
                # SRTM files start with specific byte pattern
                header = f.read(4)
                if len(header) < 4:
                    return False
                
                # Check if it's a valid SRTM format
                # This is a basic check - could be enhanced
                return True
                
        except Exception:
            return False
    
    def download_srtm_tile(self, lat: float, lon: float, 
                          progress_callback: Optional[Callable[[int], None]] = None) -> Optional[str]:
        """
        Download SRTM tile for given coordinates.
        
        Args:
            lat: Latitude in decimal degrees
            lon: Longitude in decimal degrees
            progress_callback: Optional callback for download progress (0-100)
            
        Returns:
            Path to downloaded file if successful, None otherwise
        """
        try:
            # Check if file already exists
            local_filepath = self.get_local_filepath(lat, lon)
            if local_filepath.exists() and self.validate_downloaded_file(local_filepath):
                if progress_callback:
                    progress_callback(100)
                return str(local_filepath)
            
            # Generate download URL
            url = self.get_srtm_url(lat, lon)
            filename = self.get_srtm_filename(lat, lon)
            
            # Start download
            if progress_callback:
                progress_callback(0)
            
            response = self.session.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            # Get total file size for progress tracking
            total_size = int(response.headers.get('content-length', 0))
            
            # Download file with progress tracking
            downloaded_size = 0
            with open(local_filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # Update progress
                        if progress_callback and total_size > 0:
                            progress = int((downloaded_size / total_size) * 100)
                            progress_callback(progress)
            
            # Validate downloaded file
            if self.validate_downloaded_file(local_filepath):
                if progress_callback:
                    progress_callback(100)
                return str(local_filepath)
            else:
                # Remove invalid file
                if local_filepath.exists():
                    local_filepath.unlink()
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"Download error for {lat}, {lon}: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error downloading SRTM tile: {e}")
            return None
    
    def download_with_retry(self, lat: float, lon: float, 
                           max_retries: int = 3,
                           progress_callback: Optional[Callable[[int], None]] = None) -> Optional[str]:
        """
        Download SRTM tile with retry logic.
        
        Args:
            lat: Latitude in decimal degrees
            lon: Longitude in decimal degrees
            max_retries: Maximum number of retry attempts
            progress_callback: Optional callback for download progress
            
        Returns:
            Path to downloaded file if successful, None otherwise
        """
        for attempt in range(max_retries):
            try:
                result = self.download_srtm_tile(lat, lon, progress_callback)
                if result:
                    return result
                
                # Wait before retry
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                    
            except Exception as e:
                print(f"Download attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
        
        return None
    
    def cleanup_session(self):
        """Clean up the download session."""
        if self.session:
            self.session.close()


def download_srtm_tile(lat: float, lon: float, 
                      cache_dir: str = "srtm_data",
                      progress_callback: Optional[Callable[[int], None]] = None) -> Optional[str]:
    """
    Convenience function to download SRTM tile.
    
    Args:
        lat: Latitude in decimal degrees
        lon: Longitude in decimal degrees
        cache_dir: Directory to store downloaded files
        progress_callback: Optional callback for download progress
        
    Returns:
        Path to downloaded file if successful, None otherwise
    """
    downloader = SRTMDownloader(cache_dir)
    try:
        return downloader.download_with_retry(lat, lon, progress_callback=progress_callback)
    finally:
        downloader.cleanup_session() 