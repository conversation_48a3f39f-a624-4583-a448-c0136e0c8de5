"""
Distance Calculator Module
Provides radar coverage and range calculations for the viewshed analysis tool.
"""

import math
import numpy as np

class DistanceCalculator:
    """
    Class for radar coverage and distance calculations.
    """
    
    def __init__(self):
        self.last_calculation = None
    
    def calculate_radar_coverage(self, radar_height, elevation_angle, target_altitude, max_range, frequency=16.4):
        """
        Calculate radar range and coverage parameters.
        
        Args:
            radar_height (float): Radar height in meters
            elevation_angle (float): Elevation angle in degrees
            target_altitude (float): Target altitude in meters
            max_range (float): Maximum range in kilometers
            frequency (float): Frequency in GHz (not used in geometric calculations)
        
        Returns:
            dict: Dictionary containing calculated values
        """
        # Convert max_range to meters
        max_range_m = max_range * 1000
        
        # Calculate half elevation angle in radians
        half_elevation_rad = math.radians(elevation_angle / 2)
        
        # 1. Maximum Beam Height (Bh)
        # Simplified calculation assuming a conical beam
        max_beam_height = radar_height + max_range_m * math.tan(half_elevation_rad)
        
        # 2. Ground Range at Radar Height (Rg)
        ground_range = radar_height / math.tan(half_elevation_rad) if half_elevation_rad > 0 else 0
        
        # 3. Range to Target at Altitude (Rt)
        if target_altitude <= max_beam_height:
            if target_altitude > radar_height:
                # Target is above radar height
                horizontal_range_m = (target_altitude - radar_height) / math.tan(half_elevation_rad)
                if horizontal_range_m <= max_range_m:
                    range_to_target = horizontal_range_m / 1000  # Convert to km
                    within_coverage = True
                else:
                    range_to_target = None
                    within_coverage = False
            else:
                # Target is at or below radar height
                range_to_target = 0
                within_coverage = True
        else:
            range_to_target = None
            within_coverage = False
        
        # Store calculation results
        self.last_calculation = {
            'max_beam_height': max_beam_height,
            'ground_range': ground_range,
            'range_to_target': range_to_target,
            'within_coverage': within_coverage,
            'input_parameters': {
                'radar_height': radar_height,
                'elevation_angle': elevation_angle,
                'target_altitude': target_altitude,
                'max_range': max_range,
                'frequency': frequency
            }
        }
        
        return self.last_calculation
    
    def get_coverage_summary(self):
        """
        Get a formatted summary of the last calculation.
        
        Returns:
            str: Formatted summary string
        """
        if not self.last_calculation:
            return "No calculation performed yet."
        
        calc = self.last_calculation
        params = calc['input_parameters']
        
        # Format range to target
        range_text = f"{calc['range_to_target']:.2f} km" if calc['range_to_target'] else "Out of Range"
        coverage_text = "Yes" if calc['within_coverage'] else "No"
        
        summary = f"""
=== Radar Coverage Analysis ===
Input Parameters:
  • Radar Height: {params['radar_height']:.1f} m
  • Elevation Angle: {params['elevation_angle']:.1f}°
  • Target Altitude: {params['target_altitude']:.1f} m
  • Maximum Range: {params['max_range']:.1f} km
  • Frequency: {params['frequency']:.1f} GHz

Calculated Results:
  • Maximum Beam Height: {calc['max_beam_height']:.1f} m
  • Ground Range at Radar Height: {calc['ground_range']:.1f} m
  • Range to Target: {range_text}
  • Target Within Coverage: {coverage_text}
"""
        
        return summary.strip()
    
    def calculate_line_of_sight_range(self, radar_height, target_height, earth_radius=6371000):
        """
        Calculate theoretical line-of-sight range considering Earth's curvature.
        
        Args:
            radar_height (float): Radar height in meters
            target_height (float): Target height in meters
            earth_radius (float): Earth's radius in meters
        
        Returns:
            float: Line-of-sight range in kilometers
        """
        # Calculate geometric line-of-sight range
        range_radar = math.sqrt(2 * earth_radius * radar_height + radar_height**2)
        range_target = math.sqrt(2 * earth_radius * target_height + target_height**2)
        
        total_range = (range_radar + range_target) / 1000  # Convert to km
        
        return total_range
    
    def calculate_fresnel_zone_radius(self, distance, frequency, zone_number=1):
        """
        Calculate Fresnel zone radius at a given distance.
        
        Args:
            distance (float): Distance in meters
            frequency (float): Frequency in GHz
            zone_number (int): Fresnel zone number (usually 1)
        
        Returns:
            float: Fresnel zone radius in meters
        """
        # Convert frequency to Hz
        freq_hz = frequency * 1e9
        
        # Speed of light
        c = 3e8
        
        # Wavelength
        wavelength = c / freq_hz
        
        # Fresnel zone radius
        # For the first zone at mid-point of the path
        radius = math.sqrt(zone_number * wavelength * distance / 2)
        
        return radius
    
    def analyze_radar_parameters(self, radar_height, elevation_angle, target_altitude, 
                               max_range, frequency):
        """
        Perform comprehensive radar parameter analysis.
        
        Args:
            radar_height (float): Radar height in meters
            elevation_angle (float): Elevation angle in degrees
            target_altitude (float): Target altitude in meters
            max_range (float): Maximum range in kilometers
            frequency (float): Frequency in GHz
        
        Returns:
            dict: Comprehensive analysis results
        """
        # Basic coverage calculation
        coverage = self.calculate_radar_coverage(
            radar_height, elevation_angle, target_altitude, max_range, frequency
        )
        
        # Line-of-sight calculation
        los_range = self.calculate_line_of_sight_range(radar_height, target_altitude)
        
        # Fresnel zone analysis (at maximum range)
        fresnel_radius = self.calculate_fresnel_zone_radius(
            max_range * 1000, frequency
        )
        
        # Combine all results
        analysis = {
            **coverage,
            'line_of_sight_range': los_range,
            'fresnel_zone_radius': fresnel_radius,
            'analysis_notes': []
        }
        
        # Add analysis notes
        if analysis['range_to_target'] and analysis['range_to_target'] > los_range:
            analysis['analysis_notes'].append(
                "WARNING: Target range exceeds line-of-sight limit"
            )
        
        if analysis['within_coverage'] and analysis['range_to_target']:
            if analysis['range_to_target'] > max_range * 0.8:
                analysis['analysis_notes'].append(
                    "INFO: Target near maximum range limit"
                )
        
        return analysis 