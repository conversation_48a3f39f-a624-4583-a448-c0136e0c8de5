import numpy as np
from scipy.ndimage import gaussian_filter, binary_closing, sobel

def smooth_coverage_map(coverage_array, sigma=1.0):
    """
    Apply Gaussian smoothing to the coverage map.
    
    Args:
        coverage_array (np.array): Boolean array of coverage data
        sigma (float): Standard deviation for Gaussian kernel
        
    Returns:
        np.array: Smoothed boolean array
    """
    smoothed = gaussian_filter(coverage_array.astype(float), sigma=sigma)
    return smoothed > 0.5  # Convert back to boolean with threshold

def fill_small_gaps(coverage_array):
    """
    Fill small gaps in the coverage using morphological closing.
    
    Args:
        coverage_array (np.array): Boolean array of coverage data
        
    Returns:
        np.array: Coverage array with small gaps filled
    """
    structure = np.ones((3,3))  # 3x3 structuring element
    filled = binary_closing(coverage_array, structure=structure)
    return filled

def enhance_edges(coverage_array, edge_weight=0.3):
    """
    Enhance edges of the coverage area using Sobel operator.
    
    Args:
        coverage_array (np.array): Boolean array of coverage data
        edge_weight (float): Weight of edge enhancement (0-1)
        
    Returns:
        np.array: Coverage array with enhanced edges
    """
    edges = sobel(coverage_array.astype(float))
    enhanced = np.clip(coverage_array.astype(float) + edge_weight * edges, 0, 1)
    return enhanced

def apply_all_enhancements(coverage_array, sigma=1.0, edge_weight=0.3):
    """
    Apply all enhancement techniques in sequence.
    
    Args:
        coverage_array (np.array): Boolean array of coverage data
        sigma (float): Standard deviation for Gaussian smoothing
        edge_weight (float): Weight of edge enhancement
        
    Returns:
        np.array: Enhanced coverage array
    """
    # First fill small gaps
    filled = fill_small_gaps(coverage_array)
    
    # Then apply smoothing
    smoothed = smooth_coverage_map(filled, sigma)
    
    # Finally enhance edges
    enhanced = enhance_edges(smoothed, edge_weight)
    
    return enhanced
