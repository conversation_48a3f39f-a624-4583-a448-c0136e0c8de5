from visibility_2d import Visibility2DAnalyzer
import os
import sys
from datetime import datetime
import numpy as np

def get_hgt_filename(lat, lon):
    """
    Determine the HGT filename based on latitude and longitude.

    Args:
        lat (float): Latitude in decimal degrees.
        lon (float): Longitude in decimal degrees.

    Returns:
        str: The HGT filename (e.g., "N55E026.hgt").
             Returns None if the filename cannot be determined.
    """
    lat_hemisphere = "N" if lat >= 0 else "S"
    lon_hemisphere = "E" if lon >= 0 else "W"

    lat_val = abs(int(lat))
    lon_val = abs(int(lon))

    return f"{lat_hemisphere}{lat_val:02}{lon_hemisphere}{lon_val:03}.hgt"

def validate_coordinates(lat, lon, hgt_filepath):
    """Validate if coordinates fall within the HGT tile bounds."""
    if not os.path.exists(hgt_filepath):
        raise FileNotFoundError(f"HGT file not found: {hgt_filepath}")

    # Extract tile bounds from filename
    filename = os.path.basename(hgt_filepath)
    tile_lat = int(filename[1:3])
    tile_lon = int(filename[4:7])

    if filename[0] == 'S':
        tile_lat = -tile_lat
    if filename[3] == 'W':
        tile_lon = -tile_lon

    # Check if coordinates fall within tile bounds
    if not (tile_lat <= lat < tile_lat + 1 and
            tile_lon <= lon < tile_lon + 1):
        raise ValueError(
            f"Observer coordinates ({lat}, {lon}) fall outside the HGT tile bounds "
            f"({tile_lat}° to {tile_lat + 1}°, {tile_lon}° to {tile_lon + 1}°)"
        )

def format_coordinates(lat, lon):
    """Format coordinates in a human-readable way."""
    lat_dir = "N" if lat >= 0 else "S"
    lon_dir = "E" if lon >= 0 else "W"
    return f"{abs(lat):.6f}°{lat_dir}, {abs(lon):.6f}°{lon_dir}"

def main():
    # Parameters
    observer_lat = 49.746148
    observer_lon = 22.470408
    observer_height = 30.0  # meters
    radius_km = 3  # Analysis radius in kilometers
    vertical_fov = 40.0  # Vertical field of view in degrees
    start_angle = 0  # Start angle in degrees (0 is East, clockwise)
    end_angle = 360  # End angle in degrees (0 is East, clockwise)

    # Get HGT filename based on coordinates
    hgt_filename = get_hgt_filename(observer_lat, observer_lon)
    hgt_filepath = os.path.join("srtm_data", hgt_filename)

    # Create output directory with meaningful name
    lat_prefix = str(observer_lat)[:5]
    lon_prefix = str(observer_lon)[:5]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = (f"Lat_{lat_prefix}_Lon_{lon_prefix}_"
                  f"H_{int(observer_height)}_FOV_{int(vertical_fov)}_"
                  f"R_{radius_km}km_SA_{start_angle}_EA_{end_angle}_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)

    print("\n=== Visibility Analysis Parameters ===")
    print(f"Observer position: {format_coordinates(observer_lat, observer_lon)}")
    print(f"Observer height: {observer_height}m")
    print(f"Analysis radius: {radius_km}km")
    print(f"Vertical FOV: ±{vertical_fov/2}°")
    print(f"Horizontal coverage: {start_angle}° to {end_angle}°")
    print(f"HGT file: {hgt_filepath}")  # Print the determined HGT file path
    print(f"Output directory: {output_dir}")

    try:
        # Validate coordinates
        print("\nValidating parameters...")
        validate_coordinates(observer_lat, observer_lon, hgt_filepath)

        # Initialize analyzer
        analyzer = Visibility2DAnalyzer(hgt_filepath, observer_lat, observer_lon) # Use hgt_filepath

        # Calculate visibility
        print("Calculating visibility...")
        visibility, bounds = analyzer.calculate_visibility(
            observer_height,
            radius_km=radius_km,
            vertical_fov=vertical_fov,
            start_angle=start_angle,
            end_angle=end_angle
        )

        # Create 2D plot
        print("Creating 2D visualization...")
        plot_file = os.path.join(output_dir, "visibility_map.png")
        analyzer.plot_2d_visibility(visibility, bounds, radius_km)

        # Export to KML
        print("Exporting to KML...")
        kml_file = os.path.join(output_dir, "visibility_analysis.kml")
        analyzer.export_to_kml(visibility, bounds, kml_file)

        print("\n=== Analysis Complete ===")
        print("Output files created in:", output_dir)
        print(f"- Visibility map: visibility_map.png")
        print(f"- KML overlay: visibility_analysis.kml")
        print("\nBounds of analysis:")
        print(f"North: {bounds['north']:.6f}°")
        print(f"South: {bounds['south']:.6f}°")
        print(f"East: {bounds['east']:.6f}°")
        print(f"West: {bounds['west']:.6f}°")

    except Exception as e:
        print(f"\nError during analysis: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()