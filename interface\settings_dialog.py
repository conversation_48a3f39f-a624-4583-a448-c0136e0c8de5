from PyQt6.QtWidgets import (<PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QFormLayout, QCheckBox,
                             QPushButton, QWidget, QComboBox, QLineEdit)
import json
from utils.constants import DEFAULT_SETTINGS_FILE, MAP_PROVIDERS

class SettingsDialog(QDialog):
    def __init__(self, parent=None, settings=None):
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.settings = settings if settings else {}
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()

        self.distance_rings_checkbox = QCheckBox("Show Distance Rings")
        self.distance_rings_checkbox.setChecked(self.settings.get('distance_rings', False))
        form_layout.addRow(self.distance_rings_checkbox)

        self.google_elevation_checkbox = QCheckBox("Use Google Elevation Data")
        self.google_elevation_checkbox.setChecked(self.settings.get('use_google_elevation', False))
        form_layout.addRow(self.google_elevation_checkbox)

        self.map_provider_combo = QComboBox()
        self.map_provider_combo.addItems(MAP_PROVIDERS)
        selected_provider = self.settings.get('map_provider', 'OpenStreetMap')
        self.map_provider_combo.setCurrentText(selected_provider)
        form_layout.addRow("Map Provider:", self.map_provider_combo)

        self.srtm_username_input = QLineEdit()
        self.srtm_username_input.setText(self.settings.get('srtm_username', ''))
        form_layout.addRow("SRTM Username:", self.srtm_username_input)

        self.srtm_password_input = QLineEdit()
        self.srtm_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.srtm_password_input.setText(self.settings.get('srtm_password', ''))
        form_layout.addRow("SRTM Password:", self.srtm_password_input)

        layout.addLayout(form_layout)

        button_layout = QVBoxLayout()
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)

        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def get_settings(self):
        return {
            'distance_rings': self.distance_rings_checkbox.isChecked(),
            'use_google_elevation': self.google_elevation_checkbox.isChecked(),
            'map_provider': self.map_provider_combo.currentText(),
            'srtm_username': self.srtm_username_input.text(),
            'srtm_password': self.srtm_password_input.text()
        }