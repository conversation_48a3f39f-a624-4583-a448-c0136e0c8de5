@echo off
echo Radar Analysis Tool - Installer
echo ================================
echo.

REM Create installation directory
set INSTALL_DIR=%PROGRAMFILES%\Radar_Analysis_Tool
echo Creating installation directory: %INSTALL_DIR%
mkdir "%INSTALL_DIR%" 2>nul

REM Copy files
echo Copying application files...
xcopy /E /I /Y "dist\Radar_Analysis_Tool" "%INSTALL_DIR%"

REM Create desktop shortcut
echo Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\Desktop
set SHORTCUT=%DESKTOP%\Radar Analysis Tool.lnk

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\Radar_Analysis_Tool.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\radar_icon.ico'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
set START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs
mkdir "%START_MENU%\Radar Analysis Tool" 2>nul

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\Radar Analysis Tool\Radar Analysis Tool.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Radar_Analysis_Tool.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\radar_icon.ico'; $Shortcut.Save()"

echo.
echo Installation completed successfully!
echo The Radar Analysis Tool is now available in:
echo - Desktop shortcut
echo - Start Menu
echo - %INSTALL_DIR%
echo.
pause
