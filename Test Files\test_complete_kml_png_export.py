#!/usr/bin/env python3
"""
Comprehensive test for KML and PNG export functionality.
This test verifies that the raster-based KML export creates both KML and PNG files correctly.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt

# Add the parent directory to the path to import the main module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from visibility_2d import Visibility2DAnalyzer

def test_complete_kml_png_export():
    """Test complete KML and PNG export functionality."""
    print("Testing Complete KML and PNG Export")
    print("=" * 60)
    
    # Test parameters
    test_lat = 50.0
    test_lon = -4.0
    test_hgt_file = "srtm_data/N50W004.hgt"
    
    # Create analyzer
    analyzer = Visibility2DAnalyzer(test_hgt_file, test_lat, test_lon)
    
    # Create test visibility data
    print("Creating test visibility data...")
    size = 200
    visibility = np.zeros((size, size), dtype=bool)
    
    # Create multiple circular visible areas to test complex patterns
    center = size // 2
    
    # Main visible area
    radius1 = size // 4
    y, x = np.ogrid[:size, :size]
    mask1 = (x - center)**2 + (y - center)**2 <= radius1**2
    visibility[mask1] = True
    
    # Secondary visible area
    center2_x, center2_y = center + 30, center - 20
    radius2 = size // 8
    mask2 = (x - center2_x)**2 + (y - center2_y)**2 <= radius2**2
    visibility[mask2] = True
    
    # Define realistic bounds
    bounds = {
        'north': test_lat + 0.05,
        'south': test_lat - 0.05,
        'east': test_lon + 0.05,
        'west': test_lon - 0.05
    }
    
    print(f"Test configuration:")
    print(f"  Observer: {test_lat:.6f}°N, {test_lon:.6f}°E")
    print(f"  Visibility shape: {visibility.shape}")
    print(f"  Visible pixels: {np.sum(visibility)}")
    print(f"  Bounds: {bounds}")
    
    # Test different export scenarios
    test_scenarios = [
        {
            'name': 'Basic Red Export',
            'filename': 'Test Files/test_basic_red.kml',
            'params': {
                'enable_rings': True,
                'ring_interval': 1,
                'detection_distance': 5,
                'opacity': 0.8,
                'color_pattern': 'Red-Blue Gradient'
            }
        },
        {
            'name': 'Green with No Rings',
            'filename': 'Test Files/test_green_no_rings.kml',
            'params': {
                'enable_rings': False,
                'ring_interval': 1,
                'detection_distance': 5,
                'opacity': 0.6,
                'color_pattern': 'Green'
            }
        },
        {
            'name': 'Blue High Opacity',
            'filename': 'Test Files/test_blue_high_opacity.kml',
            'params': {
                'enable_rings': True,
                'ring_interval': 2,
                'detection_distance': 10,
                'opacity': 1.0,
                'color_pattern': 'Blue'
            }
        },
        {
            'name': 'Yellow Low Opacity',
            'filename': 'Test Files/test_yellow_low_opacity.kml',
            'params': {
                'enable_rings': True,
                'ring_interval': 1,
                'detection_distance': 3,
                'opacity': 0.4,
                'color_pattern': 'Yellow'
            }
        }
    ]
    
    results = []
    
    for scenario in test_scenarios:
        print(f"\nTesting: {scenario['name']}")
        print("-" * 40)
        
        try:
            # Run the export
            analyzer.export_to_kml(
                visibility=visibility,
                bounds=bounds,
                output_file=scenario['filename'],
                **scenario['params']
            )
            
            # Check if files were created
            kml_file = scenario['filename']
            overlay_file = scenario['filename'].replace('.kml', '_overlay.png')
            
            kml_exists = os.path.exists(kml_file)
            png_exists = os.path.exists(overlay_file)
            
            result = {
                'name': scenario['name'],
                'kml_file': kml_file,
                'overlay_file': overlay_file,
                'kml_exists': kml_exists,
                'png_exists': png_exists,
                'kml_size': os.path.getsize(kml_file) if kml_exists else 0,
                'png_size': os.path.getsize(overlay_file) if png_exists else 0,
                'success': kml_exists and png_exists
            }
            
            results.append(result)
            
            if result['success']:
                print(f"✓ {scenario['name']} - SUCCESS")
                print(f"  KML: {kml_file} ({result['kml_size']} bytes)")
                print(f"  PNG: {overlay_file} ({result['png_size']} bytes)")
            else:
                print(f"✗ {scenario['name']} - FAILED")
                if not kml_exists:
                    print(f"  Missing KML: {kml_file}")
                if not png_exists:
                    print(f"  Missing PNG: {overlay_file}")
                    
        except Exception as e:
            print(f"✗ {scenario['name']} - ERROR: {str(e)}")
            import traceback
            traceback.print_exc()
            
            results.append({
                'name': scenario['name'],
                'success': False,
                'error': str(e)
            })
    
    # Summary
    print("\n" + "=" * 60)
    print("EXPORT TEST SUMMARY")
    print("=" * 60)
    
    successful_tests = sum(1 for r in results if r.get('success', False))
    total_tests = len(results)
    
    print(f"Tests passed: {successful_tests}/{total_tests}")
    
    for result in results:
        if result.get('success', False):
            print(f"✓ {result['name']}")
        else:
            print(f"✗ {result['name']}")
            if 'error' in result:
                print(f"  Error: {result['error']}")
    
    # Additional verification
    print("\n" + "-" * 60)
    print("FILE VERIFICATION")
    print("-" * 60)
    
    for result in results:
        if result.get('success', False):
            kml_file = result['kml_file']
            overlay_file = result['overlay_file']
            
            # Check KML content
            try:
                with open(kml_file, 'r') as f:
                    kml_content = f.read()
                    
                # Basic KML validation
                has_kml_header = '<?xml version="1.0"' in kml_content
                has_kml_namespace = 'xmlns="http://www.opengis.net/kml/2.2"' in kml_content
                has_observer_point = 'Observer Position' in kml_content
                has_ground_overlay = 'GroundOverlay' in kml_content
                has_visibility_analysis = 'Visibility Analysis' in kml_content
                
                print(f"KML Content Check - {result['name']}:")
                print(f"  ✓ XML Header: {has_kml_header}")
                print(f"  ✓ KML Namespace: {has_kml_namespace}")
                print(f"  ✓ Observer Point: {has_observer_point}")
                print(f"  ✓ Ground Overlay: {has_ground_overlay}")
                print(f"  ✓ Visibility Analysis: {has_visibility_analysis}")
                
                if all([has_kml_header, has_kml_namespace, has_observer_point, has_ground_overlay, has_visibility_analysis]):
                    print(f"  ✓ KML structure is valid")
                else:
                    print(f"  ✗ KML structure has issues")
                    
            except Exception as e:
                print(f"  ✗ Error reading KML: {str(e)}")
    
    # Final result
    if successful_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED! ({successful_tests}/{total_tests})")
        print("✓ Raster-based KML export is working correctly")
        print("✓ PNG overlay generation is working correctly")
        print("✓ Multiple color patterns are supported")
        print("✓ Range rings functionality is working")
        print("✓ Opacity settings are working")
        return True
    else:
        print(f"\n❌ SOME TESTS FAILED! ({successful_tests}/{total_tests})")
        return False

if __name__ == "__main__":
    success = test_complete_kml_png_export()
    if success:
        print("\n✅ Complete KML/PNG export test PASSED!")
        sys.exit(0)
    else:
        print("\n❌ Complete KML/PNG export test FAILED!")
        sys.exit(1) 