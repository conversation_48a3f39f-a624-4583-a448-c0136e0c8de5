import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Circle
from skimage import measure

def plot_2d_visibility_with_rings(visibility, bounds, radius_km, pixel_size, 
                                observer_lat, observer_lon, observer_height):
    """Plot the visibility map with radar-style distance rings."""
    fig = plt.figure(figsize=(12, 12))
    
    # Create RGBA data
    rgba_data = np.zeros((*visibility.shape, 4))
    
    # Light fill for visible areas
    rgba_data[visibility, 0] = 1.0  # Red
    rgba_data[visibility, 3] = 0.2  # Very transparent
    
    # Plot base visibility
    plt.imshow(rgba_data, extent=bounds, interpolation='nearest')
    
    # Find and plot contours
    contours = measure.find_contours(visibility, 0.5)
    for contour in contours:
        # Convert contour coordinates to map coordinates
        x_coords = bounds[0] + (bounds[1] - bounds[0]) * contour[:, 1] / visibility.shape[1]
        y_coords = bounds[2] + (bounds[3] - bounds[2]) * contour[:, 0] / visibility.shape[0]
        plt.plot(x_coords, y_coords, 'r-', linewidth=0.5)
    
    # Add range rings
    center_x = observer_lon
    center_y = observer_lat
    
    for r in range(1000, int(radius_km * 1000) + 1000, 1000):
        # Convert meters to degrees for circle radius
        r_deg = r / (pixel_size * visibility.shape[1])
        circle = Circle((center_x, center_y), r_deg, 
                       fill=False, color='blue', alpha=0.3, linestyle='-')
        plt.gca().add_patch(circle)
        
        # Add range label
        angle = np.pi/4  # 45 degrees
        label_x = center_x + r_deg * np.cos(angle)
        label_y = center_y + r_deg * np.sin(angle)
        plt.text(label_x, label_y, f'{r}m', color='blue', alpha=0.5)
    
    plt.axis('equal')
    return fig