import os
import base64
import io
import numpy as np
import traceback
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.colors import LightSource
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QPushButton, QLabel, QFileDialog, QWidget, QHBoxLayout, QSlider
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPainter, QColor
import simplekml

# Importing tested utility functions
from utils.coordinate_utils import parse_dms_string, format_dms_string, get_srtm_bounds, decimal_to_dms

def get_coords_from_filename(filepath):
    """Extract coordinates from SRTM filename (e.g., 'N50W004.hgt'). Confirmed working method."""
    filename = os.path.basename(filepath)
    lat = int(filename[1:3])
    lon = int(filename[4:7])
    if filename[0] == 'S':
        lat = -lat
    if filename[3] == 'W':
        lon = -lon
    return lat, lon


class SRTMVerificationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("SRTM Data Verification")
        self.setMinimumSize(800, 600)
        self.elevation_data = None
        self.current_file = None
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        
        # Create matplotlib figure
        self.figure, self.ax = plt.subplots()
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Load button
        self.load_button = QPushButton("Load HGT File")
        self.load_button.clicked.connect(self.load_hgt_file)
        button_layout.addWidget(self.load_button)
        
        # Export button
        self.export_button = QPushButton("Export to KML")
        self.export_button.clicked.connect(self.export_to_kml)
        button_layout.addWidget(self.export_button)
        
        layout.addLayout(button_layout)
        
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)

    def load_hgt_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select HGT File",
            "",
            "HGT Files (*.hgt)"
        )
        
        if file_path:
            self.visualize_hgt(file_path)

    def visualize_hgt(self, file_path):
        try:
            # Store current file path
            self.current_file = file_path
            
            with open(file_path, 'rb') as f:
                data = f.read()
                data_size = len(data)
                
                # Calculate grid size from file size
                grid_points = data_size // 2
                size = int(np.sqrt(grid_points))
                
                # Create elevation array
                elevation = np.zeros((size, size), dtype=np.int16)
                
                # Read elevation data
                for i in range(size):
                    for j in range(size):
                        index = (i * size + j) * 2
                        if index + 1 < data_size:
                            val = int.from_bytes(
                                data[index:index+2], 
                                byteorder='big', 
                                signed=True
                            )
                            elevation[i, j] = val
                
                # Store elevation data
                self.elevation_data = elevation
                
                # Create visualization
                self.ax.clear()
                ls = LightSource(azdeg=315, altdeg=45)
                hillshade = ls.hillshade(elevation, vert_exag=1.0)
                
                im = self.ax.imshow(elevation, cmap='terrain', origin='upper')
                self.ax.imshow(hillshade, cmap='gray', alpha=0.3, origin='upper')
                
                self.figure.colorbar(im, ax=self.ax, label='Elevation (meters)')
                self.ax.set_title('SRTM Elevation Data')
                self.ax.set_xticks([])
                self.ax.set_yticks([])
                
                self.canvas.draw()
                
                # Calculate statistics (ignore missing data = -32768)
                valid_elevations = elevation[elevation != -32768]
                min_elev = np.min(valid_elevations)
                max_elev = np.max(valid_elevations)
                mean_elev = np.mean(valid_elevations)
                
                self.status_label.setText(
                    f"File: {os.path.basename(file_path)}\n"
                    f"Grid size: {size}x{size}\n"
                    f"Elevation range: {min_elev:.1f}m to {max_elev:.1f}m\n"
                    f"Mean elevation: {mean_elev:.1f}m\n"
                    f"Resolution: {size}x{size} points"
                )
                
        except Exception as e:
            print("Full error:")
            print(traceback.format_exc())
            self.status_label.setText(f"Error reading file: {str(e)}")

    def export_to_kml(self):
        if self.elevation_data is None or self.current_file is None:
            self.status_label.setText("Please load SRTM data first")
            return
            
        try:
            # Create opacity selection dialog
            opacity_dialog = QDialog(self)
            opacity_dialog.setWindowTitle("Select Opacity")
            layout = QVBoxLayout()
            
            # Add slider for opacity
            opacity_label = QLabel("Overlay Opacity (%):")
            layout.addWidget(opacity_label)
            
            opacity_slider = QSlider()
            opacity_slider.setOrientation(Qt.Orientation.Horizontal)
            opacity_slider.setRange(0, 100)
            opacity_slider.setValue(60)  # Default 60% opacity
            opacity_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
            opacity_slider.setTickInterval(10)
            layout.addWidget(opacity_slider)
            
            # Add value display label
            value_label = QLabel("60%")
            layout.addWidget(value_label)
            
            def update_label(value):
                value_label.setText(f"{value}%")
            opacity_slider.valueChanged.connect(update_label)
            
            # Add OK button
            ok_button = QPushButton("OK")
            ok_button.clicked.connect(opacity_dialog.accept)
            layout.addWidget(ok_button)
            
            opacity_dialog.setLayout(layout)
            
            if opacity_dialog.exec() != QDialog.DialogCode.Accepted:
                return
                
            opacity = opacity_slider.value() / 100.0  # Convert to float between 0 and 1
            
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save KML File",
                "",
                "KML Files (*.kml)"
            )
            
            if file_path:
                # Save the terrain image
                img_path = file_path.replace('.kml', '.png')
                self._save_terrain_image(img_path)
                
                # Get SW corner from filename
                sw_lat, sw_lon = get_coords_from_filename(self.current_file)
                
                # Get tile bounds from tested function
                north, south, east, west = get_srtm_bounds(sw_lat, sw_lon)
                
                # Center coordinates (not strictly needed for KML, but nice for reference)
                center_lat = south + (north - south) / 2
                center_lon = west + (east - west) / 2
                
                # Create KML
                kml = simplekml.Kml()
                ground = kml.newgroundoverlay(name='SRTM Elevation')
                
                # Set overlay href to the PNG filename (relative path)
                ground.icon.href = os.path.basename(img_path)
                
                # Full opacity white, apply chosen opacity to just alpha channel
                # Format: aabbggrr - we use the standard ABGR order for KML
                # Here we only change alpha (first two chars), rest white (ffffff)
                alpha_hex = f'{int(opacity * 255):02x}'
                ground.color = f"{alpha_hex}ffffff"
                
                ground.latlonbox.north = north
                ground.latlonbox.south = south
                ground.latlonbox.east = east
                ground.latlonbox.west = west
                
                # Add center reference point
                pnt = kml.newpoint(name="Center Reference")
                pnt.coords = [(center_lon, center_lat)]
                
                kml.save(file_path)
                
                self.status_label.setText(
                    f"Exported to {file_path}\n"
                    f"Opacity: {opacity:.0%}\n"
                    f"SW Corner: {format_dms_string(*decimal_to_dms(sw_lat, True))}, "
                    f"{format_dms_string(*decimal_to_dms(sw_lon, False))}\n"
                    f"Bounds: N:{north:.6f}° S:{south:.6f}° E:{east:.6f}° W:{west:.6f}°"
                )
                
        except Exception as e:
            print("Full error:")
            print(traceback.format_exc())
            self.status_label.setText(f"Error exporting KML: {str(e)}")

    def _save_terrain_image(self, img_path):
        """Create and save a hillshaded terrain image of the loaded elevation data."""
        export_fig = plt.figure(figsize=(10, 10))
        ax = export_fig.add_subplot(111)
        
        ls = LightSource(azdeg=315, altdeg=45)
        hillshade = ls.hillshade(self.elevation_data, vert_exag=1.0)
        
        # Plot terrain
        ax.imshow(self.elevation_data, cmap='terrain', origin='upper')
        # Add hillshade overlay
        ax.imshow(hillshade, cmap='gray', alpha=0.3, origin='upper')
        
        ax.set_axis_off()
        plt.subplots_adjust(top=1, bottom=0, right=1, left=0, hspace=0, wspace=0)
        plt.margins(0, 0)
        
        export_fig.savefig(img_path, 
                           bbox_inches='tight',
                           pad_inches=0,
                           dpi=300,
                           format='png')
        plt.close(export_fig)
