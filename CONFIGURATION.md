# Configuration Guide

## Overview

The Blighter Viewshed Analysis Tool uses a `config.json` file to store application settings and default parameters. This file is automatically created when you first run the application, but you can also create it manually or modify the existing one.

## Setup

1. **Copy the example file:**
   ```bash
   cp config.example.json config.json
   ```

2. **Edit the configuration:**
   Open `config.json` in your preferred text editor and modify the settings as needed.

## Configuration Options

### Global Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `distance_rings` | boolean | `true` | Enable/disable distance ring visualization on the map |
| `use_google_elevation` | boolean | `false` | Use Google Elevation API instead of SRTM data |
| `map_provider` | string | `"OpenStreetMap"` | Map provider for the interface (OpenStreetMap, Google, etc.) |
| `srtm_username` | string | `""` | Username for SRTM data access (if required) |
| `srtm_password` | string | `""` | Password for SRTM data access (if required) |

### Default Parameters (`last_parameters`)

These are the default values that will be loaded when the application starts:

#### Location Settings
- `latitude`: Radar station latitude (decimal degrees)
- `longitude`: Radar station longitude (decimal degrees)
- `height`: Radar antenna height above ground (meters)
- `target_height`: Target height above ground (meters)

#### Analysis Settings
- `detection_distance`: Maximum detection range (kilometers)
- `hbeam_min`: Minimum horizontal beam angle (degrees)
- `hbeam_max`: Maximum horizontal beam angle (degrees)
- `vbeam_min`: Minimum vertical beam angle (degrees)
- `vbeam_max`: Maximum vertical beam angle (degrees)

#### Visualization Settings
- `color_pattern`: Color scheme for viewshed visualization
- `opacity`: Opacity of the viewshed overlay (0.0 to 1.0)
- `enable_rings`: Enable distance rings on the map
- `ring_interval`: Distance between rings (kilometers)

#### RF Parameters
- `signal_frequency_ghz`: Signal frequency in GHz
- `terrain_reflectivity`: Terrain reflectivity coefficient
- `frequency`: Operating frequency (MHz)
- `reflectivity`: General reflectivity coefficient

#### Multipath Analysis
- `enable_multipath_rings`: Enable multipath ring visualization
- `multipath_color_scheme`: Color scheme for multipath visualization
- `multipath_opacity`: Opacity of multipath overlay
- `gain_threshold`: Gain threshold for multipath analysis (dB)
- `show_nulls_only`: Show only multipath nulls
- `show_peaks_only`: Show only multipath peaks
- `multipath_map_opacity`: Opacity of multipath on map
- `multipath_ring_style`: Style of multipath rings ("Both", "Outlines", "Filled")

#### Surface Reflectivity
- `land_reflectivity`: Reflectivity coefficient for land surfaces
- `water_reflectivity`: Reflectivity coefficient for water surfaces

## Security Notes

- **SRTM Credentials**: If you have SRTM data access credentials, add them to the `srtm_username` and `srtm_password` fields. These are currently empty by default.
- **API Keys**: If using Google Elevation API, you may need to add API keys to the configuration (implementation pending).

## File Location

The `config.json` file should be placed in the root directory of the application, alongside `main.py`.

## Backup

The application automatically saves your last used parameters to the `last_parameters` section of the config file, so your settings will be preserved between sessions.

## Troubleshooting

- If the application can't find `config.json`, it will create one with default values
- Make sure the JSON syntax is valid (use a JSON validator if unsure)
- The application will log any configuration errors to the console 