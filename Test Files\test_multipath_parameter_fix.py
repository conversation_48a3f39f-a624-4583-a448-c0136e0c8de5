#!/usr/bin/env python3
"""
Test script to verify multipath parameter conflict fix.
This test verifies that KML export works correctly when multipath rings are enabled.
"""

import os
import sys
import numpy as np

# Add the parent directory to the path to import the main module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from visibility_2d import Visibility2DAnalyzer
from utils.multipath_analyzer import MultipathAnalyzer

def test_multipath_parameter_fix():
    """Test that multipath parameter conflict is resolved."""
    print("Testing Multipath Parameter Fix")
    print("=" * 50)
    
    # Test parameters
    test_lat = 50.0
    test_lon = -4.0
    test_hgt_file = "srtm_data/N50W004.hgt"
    
    # Create analyzer and multipath analyzer
    analyzer = Visibility2DAnalyzer(test_hgt_file, test_lat, test_lon)
    multipath_analyzer = MultipathAnalyzer(frequency=16e9)
    
    # Set up multipath analyzer
    multipath_analyzer.set_geometry(
        radar_height=50.0,
        target_height=2.0,
        reflectivity_percent=30.0
    )
    multipath_analyzer.set_analysis_range(max_distance_km=5.0)
    
    # Calculate multipath gain pattern (required before generating polygons)
    multipath_analyzer.calculate_multipath_gain()
    
    # Create test visibility data
    print("Creating test visibility data...")
    size = 100
    visibility = np.zeros((size, size), dtype=bool)
    
    # Create a simple circular visible area
    center = size // 2
    radius = size // 4
    y, x = np.ogrid[:size, :size]
    mask = (x - center)**2 + (y - center)**2 <= radius**2
    visibility[mask] = True
    
    # Define bounds
    bounds = {
        'north': test_lat + 0.02,
        'south': test_lat - 0.02,
        'east': test_lon + 0.02,
        'west': test_lon - 0.02
    }
    
    # Create multipath parameters that include max_range_km
    multipath_params = {
        'max_range_km': 5.0,  # This was causing the conflict
        'effect_threshold_db': -30,  # More lenient threshold to generate rings
        'color_scheme': 'Rainbow (dB-based)',
        'opacity': 0.6,
        'show_nulls_only': False,
        'show_peaks_only': False,
        'smooth_transitions': True
    }
    
    print(f"Test configuration:")
    print(f"  Observer: {test_lat:.6f}°N, {test_lon:.6f}°E")
    print(f"  Visibility shape: {visibility.shape}")
    print(f"  Visible pixels: {np.sum(visibility)}")
    print(f"  Multipath params: {multipath_params}")
    
    # Test output file
    output_file = "Test Files/test_multipath_fix.kml"
    
    print(f"\nTesting KML export with multipath rings enabled...")
    
    try:
        # Test the export_to_kml method with multipath rings enabled
        analyzer.export_to_kml(
            visibility=visibility,
            bounds=bounds,
            output_file=output_file,
            enable_rings=True,
            ring_interval=1,
            detection_distance=5,
            opacity=0.8,
            color_pattern='Red-Blue Gradient',
            enable_multipath_rings=True,  # This should trigger the multipath code
            multipath_analyzer=multipath_analyzer,
            multipath_params=multipath_params
        )
        
        # Check if files were created
        kml_file = output_file
        overlay_file = output_file.replace('.kml', '_overlay.png')
        
        kml_exists = os.path.exists(kml_file)
        png_exists = os.path.exists(overlay_file)
        
        if kml_exists and png_exists:
            print(f"✓ SUCCESS: Both files created successfully")
            print(f"  KML: {kml_file} ({os.path.getsize(kml_file)} bytes)")
            print(f"  PNG: {overlay_file} ({os.path.getsize(overlay_file)} bytes)")
            
            # Check KML content for multipath rings
            with open(kml_file, 'r') as f:
                kml_content = f.read()
                
            has_multipath_folder = 'Multipath Rings' in kml_content
            has_multipath_polygons = 'Multipath ' in kml_content and 'km (' in kml_content
            
            print(f"\nKML Content Verification:")
            print(f"  ✓ Contains Multipath Folder: {has_multipath_folder}")
            print(f"  ✓ Contains Multipath Polygons: {has_multipath_polygons}")
            
            if has_multipath_folder and has_multipath_polygons:
                print(f"  ✓ Multipath rings successfully added to KML")
                return True
            else:
                print(f"  ✗ Multipath rings missing from KML")
                return False
                
        else:
            print(f"✗ FAILED: Files not created")
            if not kml_exists:
                print(f"  Missing KML: {kml_file}")
            if not png_exists:
                print(f"  Missing PNG: {overlay_file}")
            return False
            
    except Exception as e:
        error_msg = str(e)
        if "multiple values for keyword argument 'max_range_km'" in error_msg:
            print(f"✗ FAILED: Parameter conflict still exists!")
            print(f"  Error: {error_msg}")
            return False
        else:
            print(f"✗ FAILED: Unexpected error: {error_msg}")
            import traceback
            traceback.print_exc()
            return False

def test_multipath_params_directly():
    """Test multipath analyzer directly to ensure it works."""
    print(f"\nTesting multipath analyzer directly...")
    
    try:
        multipath_analyzer = MultipathAnalyzer(frequency=16e9)
        multipath_analyzer.set_geometry(50.0, 2.0, 30.0)
        multipath_analyzer.set_analysis_range(5.0)
        multipath_analyzer.calculate_multipath_gain()
        
                 # Test with the same parameters that caused the conflict
        multipath_params = {
            'max_range_km': 5.0,
            'effect_threshold_db': -30,  # More lenient threshold
            'color_scheme': 'Rainbow (dB-based)',
            'opacity': 0.6,
            'show_nulls_only': False,
            'show_peaks_only': False,
            'smooth_transitions': True
        }
        
        # This should work without parameter conflicts
        ring_polygons = multipath_analyzer.calculate_multipath_polygons(
            center_lat=50.0,
            center_lon=-4.0,
            ring_width_km=0.05,
            **multipath_params
        )
        
        print(f"✓ Direct multipath test successful")
        print(f"  Generated {len(ring_polygons)} ring polygons")
        return True
        
    except Exception as e:
        print(f"✗ Direct multipath test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing Multipath Parameter Conflict Fix")
    print("=" * 60)
    
    # Test direct multipath functionality
    direct_success = test_multipath_params_directly()
    
    # Test full KML export with multipath
    export_success = test_multipath_parameter_fix()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"  Direct multipath test: {'PASSED' if direct_success else 'FAILED'}")
    print(f"  KML export with multipath: {'PASSED' if export_success else 'FAILED'}")
    
    if direct_success and export_success:
        print("\n🎉 Multipath parameter fix test PASSED!")
        print("✓ Parameter conflict resolved")
        print("✓ KML export with multipath rings working")
        sys.exit(0)
    else:
        print("\n❌ Multipath parameter fix test FAILED!")
        sys.exit(1) 