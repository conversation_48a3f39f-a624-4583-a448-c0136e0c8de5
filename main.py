# Radar Analysis Tool - Main Application Entry Point
#
# Responsibilities:
# - Application initialization
# - Configuration loading
# - Main event loop
# - Error handling and logging setup
#
# Key Functions:
# - initialize_application() -> App
# - load_configuration() -> config
# - setup_logging() -> logger
import sys
import os

# Add the project root directory to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt6.QtWidgets import QApplication
from interface.main_window import MainWindow 
from interface.settings_dialog import SettingsDialog 
from interface.about_dialog import AboutDialog

def main():
    app = QApplication(sys.argv)
    main_window = MainWindow()
    main_window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()